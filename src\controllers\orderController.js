const orderService = require('../services/orderService');
const validationService = require('../services/validationService');
const logger = require('../utils/logger');
const { ERROR_CODES, SUCCESS_MESSAGES } = require('../utils/constants');
const { validationResult } = require('express-validator');

class OrderController {
  async placeOrder(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: {
            code: ERROR_CODES.VALIDATION_ERROR,
            message: 'Invalid input data',
            details: errors.array()
          }
        });
      }

      const { restaurantId, items, deliveryAddress, paymentMethod, couponCode, specialInstructions } = req.body;
      const customerId = req.user._id;

      // Validate order items
      const itemValidation = validationService.validateOrderItems(items);
      if (!itemValidation.valid) {
        return res.status(400).json({
          success: false,
          error: {
            code: ERROR_CODES.VALIDATION_ERROR,
            message: itemValidation.message
          }
        });
      }

      // Validate delivery address
      const addressValidation = validationService.validateDeliveryAddress(deliveryAddress);
      if (!addressValidation.valid) {
        return res.status(400).json({
          success: false,
          error: {
            code: ERROR_CODES.VALIDATION_ERROR,
            message: addressValidation.message
          }
        });
      }

      // Validate payment method
      const paymentValidation = validationService.validatePaymentMethod(paymentMethod);
      if (!paymentValidation.valid) {
        return res.status(400).json({
          success: false,
          error: {
            code: ERROR_CODES.VALIDATION_ERROR,
            message: paymentValidation.message
          }
        });
      }

      // Validate restaurant and menu items
      const { restaurant, validatedItems } = await orderService.validateOrderRequest(
        restaurantId,
        items,
        deliveryAddress
      );

      // Create order
      const order = await orderService.createOrder({
        restaurant,
        validatedItems,
        customerId,
        deliveryAddress,
        paymentMethod,
        couponCode,
        specialInstructions
      });

      res.status(201).json({
        success: true,
        message: SUCCESS_MESSAGES.ORDER_PLACED,
        data: {
          orderId: order._id,
          orderNumber: order.orderNumber,
          totalAmount: order.pricing.total,
          estimatedDeliveryTime: order.delivery.estimatedDeliveryTime,
          paymentDetails: paymentMethod === 'online' ? {
            paymentUrl: `https://payment-gateway.com/pay/${order._id}`,
            orderId: order._id
          } : null
        }
      });

    } catch (error) {
      logger.error('Place order error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: error.message || 'Failed to place order'
        }
      });
    }
  }

  async getOrder(req, res) {
    try {
      const { orderId } = req.params;
      const userId = req.user._id;
      const userRole = req.user.role;

      const order = await orderService.getOrderById(orderId, userId, userRole);

      res.json({
        success: true,
        message: 'Order retrieved successfully',
        data: order
      });

    } catch (error) {
      logger.error('Get order error:', error);
      const statusCode = error.message === 'Order not found' ? 404 :
                         error.message === 'Access denied' ? 403 : 500;

      res.status(statusCode).json({
        success: false,
        error: {
          code: statusCode === 404 ? ERROR_CODES.NOT_FOUND :
                statusCode === 403 ? ERROR_CODES.AUTHORIZATION_ERROR :
                ERROR_CODES.INTERNAL_ERROR,
          message: error.message || 'Failed to retrieve order'
        }
      });
    }
  }

  async updateOrderStatus(req, res) {
    try {
      const { orderId } = req.params;
      const { status, note, estimatedTime } = req.body;
      const user = req.user;

      const order = await orderService.updateOrderStatus(orderId, status, user, note, estimatedTime);

      res.json({
        success: true,
        message: 'Order status updated successfully',
        data: {
          orderId: order._id,
          status: order.currentStatus,
          timeline: order.timeline
        }
      });

    } catch (error) {
      logger.error('Update order status error:', error);
      const statusCode = error.message === 'Order not found' ? 404 :
                         error.message.includes('Cannot transition') || error.message.includes('Only') ? 403 : 500;

      res.status(statusCode).json({
        success: false,
        error: {
          code: statusCode === 404 ? ERROR_CODES.NOT_FOUND :
                statusCode === 403 ? ERROR_CODES.AUTHORIZATION_ERROR :
                ERROR_CODES.INTERNAL_ERROR,
          message: error.message || 'Failed to update order status'
        }
      });
    }
  }

  async cancelOrder(req, res) {
    try {
      const { orderId } = req.params;
      const { reason } = req.body;
      const userId = req.user._id;

      const order = await orderService.cancelOrder(orderId, userId, reason);

      res.json({
        success: true,
        message: 'Order cancelled successfully',
        data: {
          orderId: order._id,
          status: order.currentStatus,
          refundInfo: order.payment.method === 'online' ? {
            refundAmount: order.payment.refundAmount,
            refundStatus: 'pending',
            estimatedRefundTime: '3-5 business days'
          } : null
        }
      });

    } catch (error) {
      logger.error('Cancel order error:', error);
      const statusCode = error.message === 'Order not found' ? 404 :
                         error.message.includes('can only cancel') || error.message.includes('cannot be cancelled') ? 400 : 500;

      res.status(statusCode).json({
        success: false,
        error: {
          code: statusCode === 404 ? ERROR_CODES.NOT_FOUND :
                statusCode === 400 ? ERROR_CODES.VALIDATION_ERROR :
                ERROR_CODES.INTERNAL_ERROR,
          message: error.message || 'Failed to cancel order'
        }
      });
    }
  }
}

module.exports = new OrderController();

module.exports = new OrderController();
