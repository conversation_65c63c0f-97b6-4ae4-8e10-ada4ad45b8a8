const express = require('express');
const authMiddleware = require('../middleware/auth');
const dashboardController = require('../controllers/dashboardController');
const { USER_ROLES } = require('../utils/constants');

const router = express.Router();

// Apply authentication and authorization middleware
router.use(authMiddleware.authenticate);
router.use(authMiddleware.authorize([USER_ROLES.SUPER_ADMIN, USER_ROLES.FRANCHISE_ADMIN]));

// Dashboard routes
router.get('/stats', dashboardController.getStats);
router.get('/franchises', dashboardController.getFranchises);
router.get('/users', dashboardController.getUsers);
router.get('/orders', dashboardController.getOrders);

module.exports = router;
