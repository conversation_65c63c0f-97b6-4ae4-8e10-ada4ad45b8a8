<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Food Delivery Real-time Client</title>
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .event { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <h1>🍕 Food Delivery Real-time Client</h1>
    
    <div class="container">
        <h2>Connection Status</h2>
        <div id="connectionStatus" class="status disconnected">Disconnected</div>
        <div>
            <input type="text" id="jwtToken" placeholder="Enter JWT Token" style="width: 300px;">
            <button onclick="connect()">Connect</button>
            <button onclick="disconnect()">Disconnect</button>
        </div>
        <p><small>Get JWT token by logging in via API: POST /api/v1/auth/login then POST /api/v1/auth/verify-otp with OTP: 123456</small></p>
    </div>

    <div class="grid">
        <div class="container">
            <h2>🎭 User Role Simulation</h2>
            <select id="userRole">
                <option value="customer">Customer</option>
                <option value="restaurant_owner">Restaurant Owner</option>
                <option value="delivery_partner">Delivery Partner</option>
                <option value="admin">Admin</option>
            </select>
            <button onclick="switchRole()">Switch Role</button>
            
            <h3>Customer Actions</h3>
            <button onclick="simulateOrderPlacement()">Place Order</button>
            <button onclick="simulateOrderCancellation()">Cancel Order</button>
            
            <h3>Restaurant Actions</h3>
            <button onclick="updateRestaurantStatus()">Toggle Restaurant Status</button>
            <button onclick="updateOrderStatus('accepted')">Accept Order</button>
            <button onclick="updateOrderStatus('preparing')">Start Preparing</button>
            <button onclick="updateOrderStatus('ready')">Mark Ready</button>
            
            <h3>Delivery Partner Actions</h3>
            <button onclick="updateLocation()">Update Location</button>
            <button onclick="toggleAvailability()">Toggle Availability</button>
            <button onclick="updateOrderStatus('picked_up')">Pick Up Order</button>
            <button onclick="updateOrderStatus('out_for_delivery')">Out for Delivery</button>
            <button onclick="updateOrderStatus('delivered')">Mark Delivered</button>
        </div>

        <div class="container">
            <h2>📍 Live Tracking Simulation</h2>
            <div>
                <label>Latitude: <input type="number" id="latitude" value="19.0760" step="0.0001"></label>
                <label>Longitude: <input type="number" id="longitude" value="72.8777" step="0.0001"></label>
                <button onclick="sendLocationUpdate()">Send Location</button>
                <button onclick="startLocationTracking()">Start Auto Tracking</button>
                <button onclick="stopLocationTracking()">Stop Auto Tracking</button>
            </div>
            <div id="locationStatus"></div>
        </div>
    </div>

    <div class="container">
        <h2>📊 Real-time Events Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <button onclick="testMockEvents()">Test Mock Events</button>
        <div id="eventLog" class="log"></div>
    </div>

    <script>
        let socket = null;
        let locationInterval = null;
        let isAvailable = false;
        let isRestaurantOpen = true;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('eventLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateConnectionStatus(connected) {
            const statusDiv = document.getElementById('connectionStatus');
            if (connected) {
                statusDiv.textContent = 'Connected to Real-time Service';
                statusDiv.className = 'status connected';
            } else {
                statusDiv.textContent = 'Disconnected';
                statusDiv.className = 'status disconnected';
            }
        }

        function connect() {
            const token = document.getElementById('jwtToken').value;
            if (!token) {
                alert('Please enter JWT token');
                return;
            }

            socket = io('http://localhost:3000', {
                auth: { token },
                transports: ['websocket', 'polling']
            });

            socket.on('connect', () => {
                log('Connected to Socket.io server', 'success');
                updateConnectionStatus(true);
            });

            socket.on('connected', (data) => {
                log(`Connection confirmed: ${JSON.stringify(data)}`, 'success');
            });

            socket.on('disconnect', () => {
                log('Disconnected from server', 'warning');
                updateConnectionStatus(false);
            });

            socket.on('error', (error) => {
                log(`Socket error: ${error}`, 'error');
            });

            // Order events
            socket.on('order-status-update', (data) => {
                log(`Order Status Update: ${JSON.stringify(data)}`, 'event');
            });

            socket.on('new-order', (data) => {
                log(`New Order Received: ${JSON.stringify(data)}`, 'event');
            });

            // Delivery events
            socket.on('delivery-location-update', (data) => {
                log(`Delivery Location Update: ${JSON.stringify(data)}`, 'event');
            });

            socket.on('delivery-partner-assigned', (data) => {
                log(`Delivery Partner Assigned: ${JSON.stringify(data)}`, 'event');
            });

            // Restaurant events
            socket.on('restaurant-status-update', (data) => {
                log(`Restaurant Status Update: ${JSON.stringify(data)}`, 'event');
            });

            // Notifications
            socket.on('notification', (data) => {
                log(`Notification: ${JSON.stringify(data)}`, 'event');
            });

            // General events
            socket.on('delivery-partner-location', (data) => {
                log(`Delivery Partner Location: ${JSON.stringify(data)}`, 'event');
            });

            socket.on('delivery-partner-availability', (data) => {
                log(`Delivery Partner Availability: ${JSON.stringify(data)}`, 'event');
            });
        }

        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
                updateConnectionStatus(false);
                log('Manually disconnected', 'info');
            }
        }

        function sendLocationUpdate() {
            if (!socket) {
                alert('Not connected to socket');
                return;
            }

            const latitude = parseFloat(document.getElementById('latitude').value);
            const longitude = parseFloat(document.getElementById('longitude').value);

            socket.emit('location-update', {
                latitude,
                longitude,
                orderId: 'ORD-20241201-1234', // Mock order ID
                isAvailable
            });

            log(`Location update sent: ${latitude}, ${longitude}`, 'info');
        }

        function startLocationTracking() {
            if (locationInterval) return;

            locationInterval = setInterval(() => {
                // Simulate movement
                const lat = document.getElementById('latitude');
                const lng = document.getElementById('longitude');
                
                lat.value = (parseFloat(lat.value) + (Math.random() - 0.5) * 0.001).toFixed(6);
                lng.value = (parseFloat(lng.value) + (Math.random() - 0.5) * 0.001).toFixed(6);
                
                sendLocationUpdate();
            }, 5000); // Update every 5 seconds

            log('Started automatic location tracking', 'info');
        }

        function stopLocationTracking() {
            if (locationInterval) {
                clearInterval(locationInterval);
                locationInterval = null;
                log('Stopped automatic location tracking', 'info');
            }
        }

        function toggleAvailability() {
            if (!socket) {
                alert('Not connected to socket');
                return;
            }

            isAvailable = !isAvailable;
            socket.emit('availability-status', { isAvailable });
            log(`Availability toggled: ${isAvailable}`, 'info');
        }

        function updateRestaurantStatus() {
            if (!socket) {
                alert('Not connected to socket');
                return;
            }

            isRestaurantOpen = !isRestaurantOpen;
            socket.emit('restaurant-status', {
                restaurantId: '507f1f77bcf86cd799439013',
                isOpen: isRestaurantOpen,
                acceptingOrders: isRestaurantOpen
            });
            log(`Restaurant status: ${isRestaurantOpen ? 'Open' : 'Closed'}`, 'info');
        }

        function simulateOrderPlacement() {
            fetch('http://localhost:3000/api/v1/mock/order-status-update')
                .then(response => response.json())
                .then(data => log(`Mock order placement triggered: ${JSON.stringify(data)}`, 'info'))
                .catch(error => log(`Error: ${error}`, 'error'));
        }

        function simulateOrderCancellation() {
            log('Order cancellation simulated', 'info');
        }

        function updateOrderStatus(status) {
            log(`Order status update simulated: ${status}`, 'info');
        }

        function testMockEvents() {
            // Test order status update
            fetch('http://localhost:3000/api/v1/mock/order-status-update')
                .then(response => response.json())
                .then(data => log(`Mock order status update: ${JSON.stringify(data)}`, 'info'));

            // Test delivery location update
            setTimeout(() => {
                fetch('http://localhost:3000/api/v1/mock/delivery-location')
                    .then(response => response.json())
                    .then(data => log(`Mock delivery location: ${JSON.stringify(data)}`, 'info'));
            }, 2000);
        }

        function clearLog() {
            document.getElementById('eventLog').textContent = '';
        }

        function switchRole() {
            const role = document.getElementById('userRole').value;
            log(`Switched to role: ${role}`, 'info');
        }

        // Initialize
        log('Real-time client initialized. Connect with JWT token to start receiving events.', 'info');
        log('To get JWT token: 1) POST /api/v1/auth/login 2) POST /api/v1/auth/verify-otp with OTP: 123456', 'info');
    </script>
</body>
</html>
