const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const logger = require('../utils/logger');
const { USER_ROLES } = require('../utils/constants');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socketId mapping
    this.deliveryPartners = new Map(); // partnerId -> location data
    this.restaurantSockets = new Map(); // restaurantId -> socketId mapping
  }

  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: ["http://localhost:3000", "http://localhost:3001"],
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.setupMiddleware();
    this.setupEventHandlers();
    
    logger.info('Socket.io server initialized');
  }

  setupMiddleware() {
    // Authentication middleware for socket connections
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findById(decoded.userId).select('-password');
        
        if (!user) {
          return next(new Error('User not found'));
        }

        socket.userId = user._id.toString();
        socket.userRole = user.role;
        socket.user = user;
        
        next();
      } catch (error) {
        logger.error('Socket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });
  }

  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      this.handleConnection(socket);
      this.setupSocketEvents(socket);
    });
  }

  handleConnection(socket) {
    const { userId, userRole } = socket;
    
    // Store user connection
    this.connectedUsers.set(userId, socket.id);
    
    // Join user to appropriate rooms based on role
    socket.join(`user:${userId}`);
    
    if (userRole === USER_ROLES.RESTAURANT_OWNER) {
      // Join restaurant-specific room
      if (socket.user.profile?.restaurantInfo?.restaurantIds) {
        socket.user.profile.restaurantInfo.restaurantIds.forEach(restaurantId => {
          socket.join(`restaurant:${restaurantId}`);
          this.restaurantSockets.set(restaurantId.toString(), socket.id);
        });
      }
    }
    
    if (userRole === USER_ROLES.DELIVERY_PARTNER) {
      socket.join('delivery-partners');
    }
    
    if (userRole === USER_ROLES.FRANCHISE_ADMIN || userRole === USER_ROLES.SUPER_ADMIN) {
      socket.join('admins');
    }

    logger.info(`User ${userId} connected with role ${userRole}`);
    
    // Send connection confirmation
    socket.emit('connected', {
      message: 'Connected to real-time service',
      userId,
      role: userRole,
      timestamp: new Date().toISOString()
    });
  }

  setupSocketEvents(socket) {
    // Handle disconnection
    socket.on('disconnect', () => {
      this.handleDisconnection(socket);
    });

    // Handle delivery partner location updates
    socket.on('location-update', (data) => {
      this.handleLocationUpdate(socket, data);
    });

    // Handle delivery partner availability status
    socket.on('availability-status', (data) => {
      this.handleAvailabilityStatus(socket, data);
    });

    // Handle restaurant status updates
    socket.on('restaurant-status', (data) => {
      this.handleRestaurantStatus(socket, data);
    });

    // Handle order status acknowledgment
    socket.on('order-status-ack', (data) => {
      this.handleOrderStatusAck(socket, data);
    });

    // Handle typing indicators for chat
    socket.on('typing', (data) => {
      this.handleTyping(socket, data);
    });
  }

  handleDisconnection(socket) {
    const { userId, userRole } = socket;
    
    // Remove user from connected users
    this.connectedUsers.delete(userId);
    
    // Remove delivery partner location if applicable
    if (userRole === USER_ROLES.DELIVERY_PARTNER) {
      this.deliveryPartners.delete(userId);
    }
    
    // Remove restaurant socket mapping
    if (userRole === USER_ROLES.RESTAURANT_OWNER && socket.user.profile?.restaurantInfo?.restaurantIds) {
      socket.user.profile.restaurantInfo.restaurantIds.forEach(restaurantId => {
        this.restaurantSockets.delete(restaurantId.toString());
      });
    }

    logger.info(`User ${userId} disconnected`);
  }

  handleLocationUpdate(socket, data) {
    const { userId, userRole } = socket;
    
    if (userRole !== USER_ROLES.DELIVERY_PARTNER) {
      return socket.emit('error', { message: 'Only delivery partners can update location' });
    }

    const { latitude, longitude, orderId } = data;
    
    if (!latitude || !longitude) {
      return socket.emit('error', { message: 'Invalid location data' });
    }

    // Store delivery partner location
    this.deliveryPartners.set(userId, {
      latitude,
      longitude,
      timestamp: new Date(),
      orderId: orderId || null,
      isAvailable: data.isAvailable || false
    });

    // If delivery partner is assigned to an order, broadcast location to customer
    if (orderId) {
      this.broadcastToOrder(orderId, 'delivery-location-update', {
        deliveryPartnerId: userId,
        location: { latitude, longitude },
        timestamp: new Date().toISOString()
      });
    }

    // Broadcast to admins for tracking
    this.io.to('admins').emit('delivery-partner-location', {
      partnerId: userId,
      location: { latitude, longitude },
      orderId,
      timestamp: new Date().toISOString()
    });
  }

  handleAvailabilityStatus(socket, data) {
    const { userId, userRole } = socket;
    
    if (userRole !== USER_ROLES.DELIVERY_PARTNER) {
      return socket.emit('error', { message: 'Only delivery partners can update availability' });
    }

    const { isAvailable } = data;
    
    // Update delivery partner availability
    const partnerData = this.deliveryPartners.get(userId) || {};
    partnerData.isAvailable = isAvailable;
    partnerData.timestamp = new Date();
    this.deliveryPartners.set(userId, partnerData);

    // Broadcast to admins
    this.io.to('admins').emit('delivery-partner-availability', {
      partnerId: userId,
      isAvailable,
      timestamp: new Date().toISOString()
    });

    logger.info(`Delivery partner ${userId} availability: ${isAvailable}`);
  }

  handleRestaurantStatus(socket, data) {
    const { userId, userRole } = socket;
    
    if (userRole !== USER_ROLES.RESTAURANT_OWNER) {
      return socket.emit('error', { message: 'Only restaurant owners can update restaurant status' });
    }

    const { restaurantId, isOpen, acceptingOrders } = data;
    
    // Broadcast restaurant status to customers and admins
    this.io.emit('restaurant-status-update', {
      restaurantId,
      isOpen,
      acceptingOrders,
      timestamp: new Date().toISOString()
    });

    logger.info(`Restaurant ${restaurantId} status updated: open=${isOpen}, accepting=${acceptingOrders}`);
  }

  // Public methods for broadcasting events

  broadcastOrderUpdate(orderId, orderData) {
    if (!this.io) return;

    const { customerId, restaurantId, deliveryPartnerId, currentStatus } = orderData;

    // Broadcast to customer
    if (customerId) {
      this.io.to(`user:${customerId}`).emit('order-status-update', {
        orderId,
        status: currentStatus,
        orderData,
        timestamp: new Date().toISOString()
      });
    }

    // Broadcast to restaurant
    if (restaurantId) {
      this.io.to(`restaurant:${restaurantId}`).emit('order-status-update', {
        orderId,
        status: currentStatus,
        orderData,
        timestamp: new Date().toISOString()
      });
    }

    // Broadcast to delivery partner
    if (deliveryPartnerId) {
      this.io.to(`user:${deliveryPartnerId}`).emit('order-status-update', {
        orderId,
        status: currentStatus,
        orderData,
        timestamp: new Date().toISOString()
      });
    }

    // Broadcast to admins
    this.io.to('admins').emit('order-status-update', {
      orderId,
      status: currentStatus,
      orderData,
      timestamp: new Date().toISOString()
    });

    logger.info(`Order ${orderId} status broadcasted: ${currentStatus}`);
  }

  broadcastNewOrder(orderData) {
    if (!this.io) return;

    const { restaurantId, franchiseId } = orderData;

    // Notify restaurant
    this.io.to(`restaurant:${restaurantId}`).emit('new-order', {
      orderData,
      timestamp: new Date().toISOString()
    });

    // Notify admins
    this.io.to('admins').emit('new-order', {
      orderData,
      timestamp: new Date().toISOString()
    });

    logger.info(`New order broadcasted to restaurant ${restaurantId}`);
  }

  broadcastToOrder(orderId, event, data) {
    if (!this.io) return;

    this.io.to(`order:${orderId}`).emit(event, {
      orderId,
      ...data,
      timestamp: new Date().toISOString()
    });
  }

  broadcastToUser(userId, event, data) {
    if (!this.io) return;

    this.io.to(`user:${userId}`).emit(event, {
      ...data,
      timestamp: new Date().toISOString()
    });
  }

  broadcastToRestaurant(restaurantId, event, data) {
    if (!this.io) return;

    this.io.to(`restaurant:${restaurantId}`).emit(event, {
      restaurantId,
      ...data,
      timestamp: new Date().toISOString()
    });
  }

  getConnectedUsers() {
    return Array.from(this.connectedUsers.keys());
  }

  getDeliveryPartners() {
    return Array.from(this.deliveryPartners.entries()).map(([partnerId, data]) => ({
      partnerId,
      ...data
    }));
  }

  isUserConnected(userId) {
    return this.connectedUsers.has(userId);
  }
}

module.exports = new SocketService();
