const mongoose = require('mongoose');
const { USER_ROLES, USER_STATUS } = require('../utils/constants');

const userSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Invalid email format']
  },
  phone: {
    type: String,
    required: true,
    trim: true,
    match: [/^\+[1-9]\d{1,14}$/, 'Invalid phone number format']
  },
  role: {
    type: String,
    required: true,
    enum: Object.values(USER_ROLES),
    default: USER_ROLES.CUSTOMER
  },
  franchiseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Franchise',
    required: function() {
      return this.role !== USER_ROLES.SUPER_ADMIN;
    }
  },
  profile: {
    name: {
      type: String,
      required: true,
      trim: true,
      minlength: 2,
      maxlength: 100
    },
    avatar: {
      type: String,
      default: null
    },
    dateOfBirth: {
      type: Date,
      default: null
    },
    gender: {
      type: String,
      enum: ['male', 'female', 'other'],
      default: null
    },
    addresses: [{
      _id: {
        type: mongoose.Schema.Types.ObjectId,
        default: () => new mongoose.Types.ObjectId()
      },
      type: {
        type: String,
        enum: ['home', 'work', 'other'],
        default: 'home'
      },
      address: {
        type: String,
        required: true
      },
      coordinates: {
        type: [Number],
        validate: {
          validator: function(coords) {
            return coords.length === 2 && 
                   coords[0] >= -180 && coords[0] <= 180 && 
                   coords[1] >= -90 && coords[1] <= 90;
          },
          message: 'Invalid coordinates format'
        }
      },
      landmark: String,
      isDefault: {
        type: Boolean,
        default: false
      }
    }],
    preferences: {
      cuisines: [String],
      dietaryRestrictions: [String],
      spiceLevel: {
        type: String,
        enum: ['mild', 'medium', 'hot'],
        default: 'medium'
      }
    },
    deliveryPartnerInfo: {
      vehicleType: {
        type: String,
        enum: ['bike', 'scooter', 'bicycle', 'car'],
        required: function() {
          return this.role === USER_ROLES.DELIVERY_PARTNER;
        }
      },
      vehicleNumber: String,
      drivingLicense: String,
      documents: {
        license: String,
        rc: String,
        insurance: String,
        photo: String,
        aadhar: String,
        pan: String
      },
      currentLocation: {
        type: [Number],
        default: null
      },
      isOnline: {
        type: Boolean,
        default: false
      },
      workingHours: {
        preferredShifts: [{
          type: String,
          enum: ['morning', 'afternoon', 'evening', 'night', 'late_night']
        }],
        availability: {
          monday: { available: Boolean, shifts: [String] },
          tuesday: { available: Boolean, shifts: [String] },
          wednesday: { available: Boolean, shifts: [String] },
          thursday: { available: Boolean, shifts: [String] },
          friday: { available: Boolean, shifts: [String] },
          saturday: { available: Boolean, shifts: [String] },
          sunday: { available: Boolean, shifts: [String] }
        }
      },
      performance: {
        rating: {
          type: Number,
          min: 0,
          max: 5,
          default: 0
        },
        totalDeliveries: {
          type: Number,
          default: 0
        },
        completionRate: {
          type: Number,
          min: 0,
          max: 1,
          default: 0
        },
        avgDeliveryTime: {
          type: Number,
          default: 0
        },
        onTimeDeliveryRate: {
          type: Number,
          min: 0,
          max: 1,
          default: 0
        }
      },
      earnings: {
        todayEarnings: {
          type: Number,
          default: 0
        },
        weeklyEarnings: {
          type: Number,
          default: 0
        },
        monthlyEarnings: {
          type: Number,
          default: 0
        },
        totalEarnings: {
          type: Number,
          default: 0
        },
        pendingSettlement: {
          type: Number,
          default: 0
        }
      },
      bankDetails: {
        accountNumber: String,
        ifsc: String,
        accountHolderName: String,
        upiId: String
      },
      emergencyContact: {
        name: String,
        phone: String,
        relation: String
      }
    },
    restaurantInfo: {
      restaurantIds: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Restaurant'
      }],
      businessType: {
        type: String,
        enum: ['restaurant', 'cloud_kitchen', 'cafe', 'bakery', 'sweet_shop'],
        default: 'restaurant'
      },
      experience: {
        type: Number,
        min: 0,
        default: 0
      },
      documents: {
        fssai: String,
        gst: String,
        tradeLicense: String,
        pan: String,
        aadhar: String
      },
      bankDetails: {
        accountNumber: String,
        ifsc: String,
        accountHolderName: String,
        upiId: String
      },
      performance: {
        totalOrders: {
          type: Number,
          default: 0
        },
        avgRating: {
          type: Number,
          min: 0,
          max: 5,
          default: 0
        },
        acceptanceRate: {
          type: Number,
          min: 0,
          max: 1,
          default: 0
        },
        avgPreparationTime: {
          type: Number,
          default: 0
        }
      },
      earnings: {
        todayEarnings: {
          type: Number,
          default: 0
        },
        weeklyEarnings: {
          type: Number,
          default: 0
        },
        monthlyEarnings: {
          type: Number,
          default: 0
        },
        totalEarnings: {
          type: Number,
          default: 0
        },
        pendingSettlement: {
          type: Number,
          default: 0
        }
      }
    }
  },
  authentication: {
    passwordHash: {
      type: String,
      required: true,
      select: false
    },
    emailVerified: {
      type: Boolean,
      default: false
    },
    phoneVerified: {
      type: Boolean,
      default: false
    },
    lastLogin: {
      type: Date,
      default: null
    },
    resetPasswordToken: {
      type: String,
      default: null
    },
    resetPasswordExpiry: {
      type: Date,
      default: null
    }
  },
  settings: {
    notifications: {
      push: {
        type: Boolean,
        default: true
      },
      sms: {
        type: Boolean,
        default: true
      },
      email: {
        type: Boolean,
        default: false
      }
    },
    language: {
      type: String,
      default: 'en'
    },
    currency: {
      type: String,
      default: 'INR'
    }
  },
  status: {
    type: String,
    enum: Object.values(USER_STATUS),
    default: USER_STATUS.ACTIVE
  }
}, {
  timestamps: true,
  toJSON: { 
    virtuals: true,
    transform: function(doc, ret) {
      delete ret.authentication;
      return ret;
    }
  },
  toObject: { virtuals: true }
});

userSchema.index({ email: 1 }, { unique: true });
userSchema.index({ phone: 1, role: 1 }, { unique: true });
userSchema.index({ franchiseId: 1 });
userSchema.index({ role: 1 });
userSchema.index({ status: 1 });
userSchema.index({ 'profile.addresses.coordinates': '2dsphere' });

userSchema.pre('save', function(next) {
  if (this.profile.addresses && this.profile.addresses.length > 0) {
    const defaultAddresses = this.profile.addresses.filter(addr => addr.isDefault);
    if (defaultAddresses.length > 1) {
      this.profile.addresses.forEach((addr, index) => {
        if (index > 0) addr.isDefault = false;
      });
    }
  }
  next();
});

module.exports = mongoose.model('User', userSchema);
