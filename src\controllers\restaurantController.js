const Restaurant = require('../models/Restaurant');
const Order = require('../models/Order');
const User = require('../models/User');
const logger = require('../utils/logger');
const { ERROR_CODES, SUCCESS_MESSAGES, RESTAURANT_STATUS, ORDER_STATUS } = require('../utils/constants');
const { validationResult } = require('express-validator');

class RestaurantController {
  async createRestaurant(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: {
            code: ERROR_CODES.VALIDATION_ERROR,
            message: 'Invalid input data',
            details: errors.array()
          }
        });
      }

      const { name, businessInfo, location, operationalInfo, menu } = req.body;
      const ownerId = req.user._id;
      const franchiseId = req.user.franchiseId;

      // Check if restaurant name already exists in the franchise
      const existingRestaurant = await Restaurant.findOne({
        name: { $regex: new RegExp(`^${name}$`, 'i') },
        franchiseId
      });

      if (existingRestaurant) {
        return res.status(409).json({
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_ENTRY,
            message: 'Restaurant with this name already exists in your franchise'
          }
        });
      }

      // Create restaurant
      const restaurant = new Restaurant({
        name,
        slug: generateSlug(name),
        franchiseId,
        ownerId,
        businessInfo,
        location,
        operationalInfo,
        menu: menu || [],
        status: RESTAURANT_STATUS.PENDING,
        verificationStatus: 'pending'
      });

      await restaurant.save();

      // Update user's restaurant info
      await User.findByIdAndUpdate(ownerId, {
        $push: { 'profile.restaurantInfo.restaurantIds': restaurant._id }
      });

      logger.info(`Restaurant created: ${name} by user: ${req.user.email}`);

      res.status(201).json({
        success: true,
        message: 'Restaurant registered successfully',
        data: {
          restaurantId: restaurant._id,
          name: restaurant.name,
          status: restaurant.status,
          verificationRequired: [
            'fssai_license',
            'gst_certificate',
            'bank_details'
          ]
        }
      });

    } catch (error) {
      logger.error('Create restaurant error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to create restaurant'
        }
      });
    }
  }

  async getRestaurant(req, res) {
    try {
      const { restaurantId } = req.params;

      const restaurant = await Restaurant.findById(restaurantId)
        .populate('franchiseId', 'name location.city')
        .populate('ownerId', 'profile.name email phone');

      if (!restaurant) {
        return res.status(404).json({
          success: false,
          error: {
            code: ERROR_CODES.NOT_FOUND,
            message: 'Restaurant not found'
          }
        });
      }

      // Check access permissions
      if (req.user.role === 'restaurant_owner' && 
          restaurant.ownerId._id.toString() !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          error: {
            code: ERROR_CODES.AUTHORIZATION_ERROR,
            message: 'Access denied'
          }
        });
      }

      res.json({
        success: true,
        message: 'Restaurant retrieved successfully',
        data: restaurant
      });

    } catch (error) {
      logger.error('Get restaurant error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to retrieve restaurant'
        }
      });
    }
  }

  async updateRestaurant(req, res) {
    try {
      const { restaurantId } = req.params;
      const updateData = req.body;

      const restaurant = await Restaurant.findById(restaurantId);
      if (!restaurant) {
        return res.status(404).json({
          success: false,
          error: {
            code: ERROR_CODES.NOT_FOUND,
            message: 'Restaurant not found'
          }
        });
      }

      // Check permissions
      if (req.user.role === 'restaurant_owner' && 
          restaurant.ownerId.toString() !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          error: {
            code: ERROR_CODES.AUTHORIZATION_ERROR,
            message: 'Access denied'
          }
        });
      }

      // Update restaurant
      const updatedRestaurant = await Restaurant.findByIdAndUpdate(
        restaurantId,
        { $set: updateData },
        { new: true, runValidators: true }
      );

      logger.info(`Restaurant updated: ${restaurant.name} by user: ${req.user.email}`);

      res.json({
        success: true,
        message: 'Restaurant updated successfully',
        data: updatedRestaurant
      });

    } catch (error) {
      logger.error('Update restaurant error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to update restaurant'
        }
      });
    }
  }

  async getMenu(req, res) {
    try {
      const { restaurantId } = req.params;

      const restaurant = await Restaurant.findById(restaurantId)
        .select('name businessInfo.cuisine operationalInfo.isCurrentlyOpen menu');

      if (!restaurant) {
        return res.status(404).json({
          success: false,
          error: {
            code: ERROR_CODES.NOT_FOUND,
            message: 'Restaurant not found'
          }
        });
      }

      // Group menu items by category
      const categorizedMenu = restaurant.menu.reduce((acc, item) => {
        const category = item.category || 'Other';
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(item);
        return acc;
      }, {});

      res.json({
        success: true,
        message: 'Menu retrieved successfully',
        data: {
          restaurantInfo: {
            id: restaurant._id,
            name: restaurant.name,
            cuisine: restaurant.businessInfo.cuisine,
            isCurrentlyOpen: restaurant.operationalInfo.isCurrentlyOpen
          },
          categories: Object.keys(categorizedMenu).map(categoryName => ({
            name: categoryName,
            items: categorizedMenu[categoryName]
          }))
        }
      });

    } catch (error) {
      logger.error('Get menu error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to retrieve menu'
        }
      });
    }
  }

  async updateMenu(req, res) {
    try {
      const { restaurantId } = req.params;
      const { menu } = req.body;

      const restaurant = await Restaurant.findById(restaurantId);
      if (!restaurant) {
        return res.status(404).json({
          success: false,
          error: {
            code: ERROR_CODES.NOT_FOUND,
            message: 'Restaurant not found'
          }
        });
      }

      // Check permissions
      if (req.user.role === 'restaurant_owner' && 
          restaurant.ownerId.toString() !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          error: {
            code: ERROR_CODES.AUTHORIZATION_ERROR,
            message: 'Access denied'
          }
        });
      }

      // Update menu
      restaurant.menu = menu;
      await restaurant.save();

      logger.info(`Menu updated for restaurant: ${restaurant.name} by user: ${req.user.email}`);

      res.json({
        success: true,
        message: 'Menu updated successfully',
        data: {
          restaurantId: restaurant._id,
          menuItemsCount: menu.length
        }
      });

    } catch (error) {
      logger.error('Update menu error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to update menu'
        }
      });
    }
  }

  async getOrders(req, res) {
    try {
      const { restaurantId } = req.params;
      const { page = 1, limit = 10, status, dateFrom, dateTo } = req.query;

      const restaurant = await Restaurant.findById(restaurantId);
      if (!restaurant) {
        return res.status(404).json({
          success: false,
          error: {
            code: ERROR_CODES.NOT_FOUND,
            message: 'Restaurant not found'
          }
        });
      }

      // Check permissions
      if (req.user.role === 'restaurant_owner' && 
          restaurant.ownerId.toString() !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          error: {
            code: ERROR_CODES.AUTHORIZATION_ERROR,
            message: 'Access denied'
          }
        });
      }

      let query = { restaurantId };

      // Add status filter
      if (status) {
        query.currentStatus = status;
      }

      // Add date range filter
      if (dateFrom || dateTo) {
        query.createdAt = {};
        if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
        if (dateTo) query.createdAt.$lte = new Date(dateTo);
      }

      const skip = (parseInt(page) - 1) * parseInt(limit);

      const [orders, total] = await Promise.all([
        Order.find(query)
          .populate('customerId', 'profile.name phone')
          .populate('deliveryPartnerId', 'profile.name phone')
          .skip(skip)
          .limit(parseInt(limit))
          .sort({ createdAt: -1 }),
        Order.countDocuments(query)
      ]);

      res.json({
        success: true,
        message: 'Restaurant orders retrieved successfully',
        data: orders,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });

    } catch (error) {
      logger.error('Get restaurant orders error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to retrieve restaurant orders'
        }
      });
    }
  }

  async updateOperationalStatus(req, res) {
    try {
      const { restaurantId } = req.params;
      const { isCurrentlyOpen, acceptingOrders } = req.body;

      const restaurant = await Restaurant.findById(restaurantId);
      if (!restaurant) {
        return res.status(404).json({
          success: false,
          error: {
            code: ERROR_CODES.NOT_FOUND,
            message: 'Restaurant not found'
          }
        });
      }

      // Check permissions
      if (req.user.role === 'restaurant_owner' && 
          restaurant.ownerId.toString() !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          error: {
            code: ERROR_CODES.AUTHORIZATION_ERROR,
            message: 'Access denied'
          }
        });
      }

      // Update operational status
      if (typeof isCurrentlyOpen !== 'undefined') {
        restaurant.operationalInfo.isCurrentlyOpen = isCurrentlyOpen;
      }
      if (typeof acceptingOrders !== 'undefined') {
        restaurant.operationalInfo.acceptingOrders = acceptingOrders;
      }

      await restaurant.save();

      logger.info(`Operational status updated for restaurant: ${restaurant.name} by user: ${req.user.email}`);

      res.json({
        success: true,
        message: 'Operational status updated successfully',
        data: {
          restaurantId: restaurant._id,
          isCurrentlyOpen: restaurant.operationalInfo.isCurrentlyOpen,
          acceptingOrders: restaurant.operationalInfo.acceptingOrders
        }
      });

    } catch (error) {
      logger.error('Update operational status error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to update operational status'
        }
      });
    }
  }
}

// Helper function
function generateSlug(name) {
  return name.toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim('-');
}

module.exports = new RestaurantController();
