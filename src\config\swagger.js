const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Food Delivery Platform API',
      version: '1.0.0',
      description: `
        A comprehensive, enterprise-grade food delivery platform API similar to Zomato.
        
        ## Features
        - **Multi-Role Support**: Customer, Restaurant Owner, Delivery Partner, Franchise Admin, Super Admin
        - **JWT Authentication**: Secure token-based authentication with refresh tokens
        - **Geospatial Search**: Location-based restaurant discovery
        - **Order Management**: Complete order lifecycle with real-time tracking
        - **Commission System**: Dynamic pricing and commission calculations
        - **Multi-Tenant**: Franchise-based architecture for scalability
        
        ## Authentication
        Most endpoints require authentication. Include the JWT token in the Authorization header:
        \`Authorization: Bearer <your-jwt-token>\`
        
        ## Getting Started
        1. Register a user with \`POST /auth/register\`
        2. Verify OTP with \`POST /auth/verify-otp\` (use OTP: 123456)
        3. Use the returned token for authenticated requests
      `,
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'http://localhost:3000/api/v1',
        description: 'Development server'
      },
      {
        url: 'https://api.fooddelivery.com/v1',
        description: 'Production server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Enter JWT token obtained from login/verify-otp'
        }
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            _id: { type: 'string', example: '507f1f77bcf86cd799439011' },
            email: { type: 'string', format: 'email', example: '<EMAIL>' },
            phone: { type: 'string', example: '+919876543210' },
            role: { 
              type: 'string', 
              enum: ['customer', 'restaurant_owner', 'delivery_partner', 'franchise_admin', 'super_admin'],
              example: 'customer'
            },
            profile: {
              type: 'object',
              properties: {
                name: { type: 'string', example: 'John Doe' },
                avatar: { type: 'string', example: 'https://example.com/avatar.jpg' },
                addresses: {
                  type: 'array',
                  items: { $ref: '#/components/schemas/Address' }
                }
              }
            },
            status: { 
              type: 'string', 
              enum: ['active', 'inactive', 'suspended', 'pending_verification'],
              example: 'active'
            },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        },
        Address: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            type: { type: 'string', enum: ['home', 'work', 'other'], example: 'home' },
            address: { type: 'string', example: '123 Main St, Mumbai, Maharashtra' },
            coordinates: { 
              type: 'array', 
              items: { type: 'number' },
              example: [72.8777, 19.0760],
              description: '[longitude, latitude]'
            },
            landmark: { type: 'string', example: 'Near Metro Station' },
            isDefault: { type: 'boolean', example: true }
          },
          required: ['type', 'address', 'coordinates']
        },
        Restaurant: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            name: { type: 'string', example: 'Burger Palace' },
            businessInfo: {
              type: 'object',
              properties: {
                cuisine: { 
                  type: 'array', 
                  items: { type: 'string' },
                  example: ['american', 'fast_food']
                },
                description: { type: 'string', example: 'Best burgers in town' }
              }
            },
            location: {
              type: 'object',
              properties: {
                address: { type: 'string', example: 'Shop 123, ABC Mall, Mumbai' },
                coordinates: { 
                  type: 'array', 
                  items: { type: 'number' },
                  example: [72.8777, 19.0760]
                }
              }
            },
            ratings: {
              type: 'object',
              properties: {
                average: { type: 'number', example: 4.2 },
                count: { type: 'number', example: 150 }
              }
            },
            operationalInfo: {
              type: 'object',
              properties: {
                isCurrentlyOpen: { type: 'boolean', example: true },
                acceptingOrders: { type: 'boolean', example: true },
                avgPreparationTime: { type: 'number', example: 25 }
              }
            }
          }
        },
        Order: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            orderNumber: { type: 'string', example: 'ORD-20241201-1234' },
            customerId: { type: 'string' },
            restaurantId: { type: 'string' },
            items: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  menuItemId: { type: 'string' },
                  name: { type: 'string', example: 'Classic Burger' },
                  price: { type: 'number', example: 250 },
                  quantity: { type: 'number', example: 2 },
                  itemTotal: { type: 'number', example: 500 }
                }
              }
            },
            pricing: {
              type: 'object',
              properties: {
                itemsSubtotal: { type: 'number', example: 500 },
                deliveryFee: { type: 'number', example: 25 },
                platformFee: { type: 'number', example: 10 },
                total: { type: 'number', example: 535 }
              }
            },
            currentStatus: { 
              type: 'string', 
              enum: ['placed', 'accepted', 'preparing', 'ready', 'picked_up', 'out_for_delivery', 'delivered', 'cancelled'],
              example: 'placed'
            },
            createdAt: { type: 'string', format: 'date-time' }
          }
        },
        ApiResponse: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            message: { type: 'string', example: 'Operation successful' },
            data: { type: 'object' }
          }
        },
        ErrorResponse: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: false },
            error: {
              type: 'object',
              properties: {
                code: { type: 'string', example: 'VALIDATION_ERROR' },
                message: { type: 'string', example: 'Invalid input data' },
                details: { type: 'array', items: { type: 'object' } }
              }
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: [
    './src/routes/*.js',
    './src/controllers/*.js',
    './src/models/*.js'
  ]
};

const specs = swaggerJsdoc(options);

const swaggerOptions = {
  customCss: `
    .swagger-ui .topbar { display: none }
    .swagger-ui .info .title { color: #ff6b35; }
    .swagger-ui .scheme-container { background: #fafafa; padding: 15px; border-radius: 5px; }
  `,
  customSiteTitle: 'Food Delivery API Documentation',
  customfavIcon: '/favicon.ico'
};

module.exports = {
  specs,
  swaggerUi,
  swaggerOptions
};
