const Restaurant = require('../models/Restaurant');
const Order = require('../models/Order');
const User = require('../models/User');
const logger = require('../utils/logger');
const { generateSlug } = require('../utils/helpers');
const { RESTAURANT_STATUS } = require('../utils/constants');

class RestaurantService {
  async createRestaurant(restaurantData, ownerId, franchiseId) {
    const { name, businessInfo, location, operationalInfo, menu } = restaurantData;

    // Check if restaurant name already exists in the franchise
    const existingRestaurant = await Restaurant.findOne({
      name: { $regex: new RegExp(`^${name}$`, 'i') },
      franchiseId
    });

    if (existingRestaurant) {
      throw new Error('Restaurant with this name already exists in your franchise');
    }

    // Create restaurant
    const restaurant = new Restaurant({
      name,
      slug: generateSlug(name),
      franchiseId,
      ownerId,
      businessInfo,
      location,
      operationalInfo,
      menu: menu || [],
      status: RESTAURANT_STATUS.PENDING,
      verificationStatus: 'pending'
    });

    await restaurant.save();

    // Update user's restaurant info
    await User.findByIdAndUpdate(ownerId, {
      $push: { 'profile.restaurantInfo.restaurantIds': restaurant._id }
    });

    logger.info(`Restaurant created: ${name} by user: ${ownerId}`);

    return restaurant;
  }

  async getRestaurantById(restaurantId, userId, userRole) {
    const restaurant = await Restaurant.findById(restaurantId)
      .populate('franchiseId', 'name location.city')
      .populate('ownerId', 'profile.name email phone');

    if (!restaurant) {
      throw new Error('Restaurant not found');
    }

    // Check access permissions
    if (!this.hasRestaurantAccess(restaurant, userId, userRole)) {
      throw new Error('Access denied');
    }

    return restaurant;
  }

  hasRestaurantAccess(restaurant, userId, userRole) {
    return (
      userRole === 'super_admin' ||
      userRole === 'franchise_admin' ||
      (userRole === 'restaurant_owner' && restaurant.ownerId._id.toString() === userId.toString())
    );
  }

  async updateRestaurant(restaurantId, updateData, userId, userRole) {
    const restaurant = await Restaurant.findById(restaurantId);
    if (!restaurant) {
      throw new Error('Restaurant not found');
    }

    // Check permissions
    if (!this.hasRestaurantAccess(restaurant, userId, userRole)) {
      throw new Error('Access denied');
    }

    // Update restaurant
    const updatedRestaurant = await Restaurant.findByIdAndUpdate(
      restaurantId,
      { $set: updateData },
      { new: true, runValidators: true }
    );

    logger.info(`Restaurant updated: ${restaurant.name} by user: ${userId}`);

    return updatedRestaurant;
  }

  async getRestaurantMenu(restaurantId) {
    const restaurant = await Restaurant.findById(restaurantId)
      .select('name businessInfo.cuisine operationalInfo.isCurrentlyOpen menu');

    if (!restaurant) {
      throw new Error('Restaurant not found');
    }

    // Group menu items by category
    const categorizedMenu = restaurant.menu.reduce((acc, item) => {
      const category = item.category || 'Other';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(item);
      return acc;
    }, {});

    return {
      restaurantInfo: {
        id: restaurant._id,
        name: restaurant.name,
        cuisine: restaurant.businessInfo.cuisine,
        isCurrentlyOpen: restaurant.operationalInfo.isCurrentlyOpen
      },
      categories: Object.keys(categorizedMenu).map(categoryName => ({
        name: categoryName,
        items: categorizedMenu[categoryName]
      }))
    };
  }

  async updateRestaurantMenu(restaurantId, menu, userId, userRole) {
    const restaurant = await Restaurant.findById(restaurantId);
    if (!restaurant) {
      throw new Error('Restaurant not found');
    }

    // Check permissions
    if (!this.hasRestaurantAccess(restaurant, userId, userRole)) {
      throw new Error('Access denied');
    }

    // Update menu
    restaurant.menu = menu;
    await restaurant.save();

    logger.info(`Menu updated for restaurant: ${restaurant.name} by user: ${userId}`);

    return {
      restaurantId: restaurant._id,
      menuItemsCount: menu.length
    };
  }

  async getRestaurantOrders(restaurantId, userId, userRole, filters = {}) {
    const restaurant = await Restaurant.findById(restaurantId);
    if (!restaurant) {
      throw new Error('Restaurant not found');
    }

    // Check permissions
    if (!this.hasRestaurantAccess(restaurant, userId, userRole)) {
      throw new Error('Access denied');
    }

    const { page = 1, limit = 10, status, dateFrom, dateTo } = filters;
    let query = { restaurantId };

    // Add status filter
    if (status) {
      query.currentStatus = status;
    }

    // Add date range filter
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
      if (dateTo) query.createdAt.$lte = new Date(dateTo);
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [orders, total] = await Promise.all([
      Order.find(query)
        .populate('customerId', 'profile.name phone')
        .populate('deliveryPartnerId', 'profile.name phone')
        .skip(skip)
        .limit(parseInt(limit))
        .sort({ createdAt: -1 }),
      Order.countDocuments(query)
    ]);

    return {
      orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    };
  }

  async updateOperationalStatus(restaurantId, statusData, userId, userRole) {
    const restaurant = await Restaurant.findById(restaurantId);
    if (!restaurant) {
      throw new Error('Restaurant not found');
    }

    // Check permissions
    if (!this.hasRestaurantAccess(restaurant, userId, userRole)) {
      throw new Error('Access denied');
    }

    const { isCurrentlyOpen, acceptingOrders } = statusData;

    // Update operational status
    if (typeof isCurrentlyOpen !== 'undefined') {
      restaurant.operationalInfo.isCurrentlyOpen = isCurrentlyOpen;
    }
    if (typeof acceptingOrders !== 'undefined') {
      restaurant.operationalInfo.acceptingOrders = acceptingOrders;
    }

    await restaurant.save();

    logger.info(`Operational status updated for restaurant: ${restaurant.name} by user: ${userId}`);

    return {
      restaurantId: restaurant._id,
      isCurrentlyOpen: restaurant.operationalInfo.isCurrentlyOpen,
      acceptingOrders: restaurant.operationalInfo.acceptingOrders
    };
  }

  async getRestaurantAnalytics(restaurantId, userId, userRole, dateRange = {}) {
    const restaurant = await Restaurant.findById(restaurantId);
    if (!restaurant) {
      throw new Error('Restaurant not found');
    }

    // Check permissions
    if (!this.hasRestaurantAccess(restaurant, userId, userRole)) {
      throw new Error('Access denied');
    }

    const { startDate, endDate } = dateRange;
    let matchCondition = { restaurantId };

    if (startDate || endDate) {
      matchCondition.createdAt = {};
      if (startDate) matchCondition.createdAt.$gte = new Date(startDate);
      if (endDate) matchCondition.createdAt.$lte = new Date(endDate);
    }

    const [
      totalOrders,
      completedOrders,
      cancelledOrders,
      totalRevenue,
      averageOrderValue,
      popularItems
    ] = await Promise.all([
      Order.countDocuments(matchCondition),
      Order.countDocuments({ ...matchCondition, currentStatus: 'delivered' }),
      Order.countDocuments({ ...matchCondition, currentStatus: 'cancelled' }),
      Order.aggregate([
        { $match: { ...matchCondition, currentStatus: 'delivered' } },
        { $group: { _id: null, total: { $sum: '$pricing.total' } } }
      ]),
      Order.aggregate([
        { $match: { ...matchCondition, currentStatus: 'delivered' } },
        { $group: { _id: null, avg: { $avg: '$pricing.total' } } }
      ]),
      Order.aggregate([
        { $match: { ...matchCondition, currentStatus: 'delivered' } },
        { $unwind: '$items' },
        { $group: { _id: '$items.name', count: { $sum: '$items.quantity' } } },
        { $sort: { count: -1 } },
        { $limit: 5 }
      ])
    ]);

    return {
      restaurantId,
      period: { startDate, endDate },
      metrics: {
        totalOrders,
        completedOrders,
        cancelledOrders,
        completionRate: totalOrders > 0 ? (completedOrders / totalOrders * 100).toFixed(2) : 0,
        totalRevenue: totalRevenue[0]?.total || 0,
        averageOrderValue: averageOrderValue[0]?.avg || 0
      },
      popularItems: popularItems.map(item => ({
        name: item._id,
        orderCount: item.count
      }))
    };
  }

  async searchRestaurants(filters = {}) {
    const {
      page = 1,
      limit = 10,
      search,
      cuisine,
      sortBy = 'rating',
      latitude,
      longitude,
      maxDistance = 10000
    } = filters;

    let query = {
      status: 'active',
      verificationStatus: 'verified'
    };

    // Add search filter
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { 'businessInfo.description': { $regex: search, $options: 'i' } },
        { 'businessInfo.cuisine': { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Add cuisine filter
    if (cuisine) {
      query['businessInfo.cuisine'] = { $in: cuisine.split(',') };
    }

    // Add location filter if coordinates provided
    if (latitude && longitude) {
      query['location.coordinates'] = {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [parseFloat(longitude), parseFloat(latitude)]
          },
          $maxDistance: parseInt(maxDistance)
        }
      };
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Build sort criteria
    let sortCriteria = {};
    switch (sortBy) {
      case 'rating':
        sortCriteria = { 'ratings.average': -1 };
        break;
      case 'deliveryTime':
        sortCriteria = { 'operationalInfo.avgPreparationTime': 1 };
        break;
      case 'popularity':
        sortCriteria = { 'performance.totalOrders': -1 };
        break;
      default:
        sortCriteria = { 'ratings.average': -1 };
    }

    const [restaurants, total] = await Promise.all([
      Restaurant.find(query)
        .skip(skip)
        .limit(parseInt(limit))
        .sort(sortCriteria)
        .select('name businessInfo location operationalInfo ratings performance menu'),
      Restaurant.countDocuments(query)
    ]);

    return {
      restaurants,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      },
      filters: { search, cuisine, sortBy, location: latitude && longitude ? { latitude, longitude, maxDistance } : null }
    };
  }
}

module.exports = new RestaurantService();
