const express = require('express');
const authMiddleware = require('../middleware/auth');
const restaurantController = require('../controllers/restaurantController');
const { USER_ROLES } = require('../utils/constants');

const router = express.Router();

// Apply authentication middleware
router.use(authMiddleware.authenticate);

// Create restaurant (restaurant owners only)
router.post('/', 
  authMiddleware.authorize([USER_ROLES.RESTAURANT_OWNER]), 
  restaurantController.createRestaurant
);

// Get restaurant details
router.get('/:restaurantId', restaurantController.getRestaurant);

// Update restaurant (restaurant owners, franchise admins, super admins)
router.put('/:restaurantId', 
  authMiddleware.authorize([USER_ROLES.RESTAURANT_OWNER, USER_ROLES.FRANCHISE_ADMIN, USER_ROLES.SUPER_ADMIN]), 
  restaurantController.updateRestaurant
);

// Get restaurant menu (public access for customers)
router.get('/:restaurantId/menu', restaurantController.getMenu);

// Update restaurant menu (restaurant owners only)
router.put('/:restaurantId/menu', 
  authMiddleware.authorize([USER_ROLES.RESTAURANT_OWNER]), 
  restaurantController.updateMenu
);

// Get restaurant orders (restaurant owners only)
router.get('/:restaurantId/orders', 
  authMiddleware.authorize([USER_ROLES.RESTAURANT_OWNER, USER_ROLES.FRANCHISE_ADMIN, USER_ROLES.SUPER_ADMIN]), 
  restaurantController.getOrders
);

// Update operational status (restaurant owners only)
router.patch('/:restaurantId/status', 
  authMiddleware.authorize([USER_ROLES.RESTAURANT_OWNER]), 
  restaurantController.updateOperationalStatus
);

module.exports = router;
