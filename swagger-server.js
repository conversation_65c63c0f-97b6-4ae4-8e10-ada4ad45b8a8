require('dotenv').config();
require('express-async-errors');

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');

const logger = require('./src/utils/logger');
const { specs, swaggerUi, swaggerOptions } = require('./src/config/swagger');

// Create Express app
const app = express();

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests from this IP, please try again later.'
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(compression());
app.use(morgan('combined'));
app.use(limiter);
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Food Delivery API with Swagger Documentation',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    version: process.env.API_VERSION || 'v1',
    documentation: {
      swagger: 'http://localhost:3000/api-docs',
      json: 'http://localhost:3000/api-docs.json'
    },
    features: {
      authentication: 'JWT + OTP',
      databases: 'MongoDB + PostgreSQL (not connected in demo)',
      apps: ['Dashboard', 'User App', 'Partner App'],
      documentation: 'Swagger UI with interactive testing'
    }
  });
});

// Swagger Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, swaggerOptions));

// API Documentation JSON endpoint
app.get('/api-docs.json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(specs);
});

// Mock API routes for demonstration
const apiVersion = process.env.API_VERSION || 'v1';
const baseRoute = `/api/${apiVersion}`;

// Mock authentication endpoints
app.post(`${baseRoute}/auth/register`, (req, res) => {
  res.status(201).json({
    success: true,
    message: 'User registered successfully, OTP sent',
    data: {
      userId: '507f1f77bcf86cd799439011',
      message: 'OTP sent to your phone number (use 123456 for testing)'
    }
  });
});

app.post(`${baseRoute}/auth/login`, (req, res) => {
  res.json({
    success: true,
    message: 'Login successful, OTP sent for verification',
    data: {
      userId: '507f1f77bcf86cd799439011',
      message: 'OTP sent to your phone number (use 123456 for testing)'
    }
  });
});

app.post(`${baseRoute}/auth/verify-otp`, (req, res) => {
  const { otp } = req.body;
  if (otp === '123456') {
    res.json({
      success: true,
      message: 'OTP verified successfully, authentication complete',
      data: {
        user: {
          _id: '507f1f77bcf86cd799439011',
          email: '<EMAIL>',
          phone: '+919876543210',
          role: 'customer',
          profile: {
            name: 'John Doe'
          },
          status: 'active'
        },
        tokens: {
          accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
          refreshToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
          expiresIn: '24h'
        }
      }
    });
  } else {
    res.status(400).json({
      success: false,
      error: {
        code: 'INVALID_OTP',
        message: 'Invalid or expired OTP. Use 123456 for testing.'
      }
    });
  }
});

// Mock user endpoints
app.get(`${baseRoute}/user/restaurants`, (req, res) => {
  res.json({
    success: true,
    message: 'Restaurants retrieved successfully',
    data: [
      {
        _id: '507f1f77bcf86cd799439012',
        name: 'Burger Palace',
        businessInfo: {
          cuisine: ['american', 'fast_food'],
          description: 'Best burgers in town'
        },
        location: {
          address: 'Shop 123, ABC Mall, Mumbai',
          coordinates: [72.8777, 19.0760]
        },
        ratings: {
          average: 4.2,
          count: 150
        },
        operationalInfo: {
          isCurrentlyOpen: true,
          acceptingOrders: true,
          avgPreparationTime: 25
        },
        distance: 2.5,
        estimatedDeliveryTime: 35
      },
      {
        _id: '507f1f77bcf86cd799439013',
        name: 'Pizza Corner',
        businessInfo: {
          cuisine: ['italian', 'pizza'],
          description: 'Authentic Italian pizzas'
        },
        location: {
          address: 'Shop 456, XYZ Street, Mumbai',
          coordinates: [72.8877, 19.0860]
        },
        ratings: {
          average: 4.5,
          count: 200
        },
        operationalInfo: {
          isCurrentlyOpen: true,
          acceptingOrders: true,
          avgPreparationTime: 30
        },
        distance: 1.8,
        estimatedDeliveryTime: 32
      }
    ],
    pagination: {
      page: 1,
      limit: 10,
      total: 2,
      pages: 1
    }
  });
});

// Mock order endpoints
app.post(`${baseRoute}/orders`, (req, res) => {
  res.status(201).json({
    success: true,
    message: 'Order placed successfully',
    data: {
      orderId: '507f1f77bcf86cd799439014',
      orderNumber: 'ORD-20241201-1234',
      totalAmount: 535,
      estimatedDeliveryTime: new Date(Date.now() + 35 * 60000).toISOString(),
      paymentDetails: req.body.paymentMethod === 'online' ? {
        paymentUrl: 'https://payment-gateway.com/pay/507f1f77bcf86cd799439014',
        orderId: '507f1f77bcf86cd799439014'
      } : null
    }
  });
});

app.get(`${baseRoute}/orders/:orderId`, (req, res) => {
  res.json({
    success: true,
    message: 'Order retrieved successfully',
    data: {
      _id: req.params.orderId,
      orderNumber: 'ORD-20241201-1234',
      customerId: '507f1f77bcf86cd799439011',
      restaurantId: '507f1f77bcf86cd799439012',
      items: [
        {
          menuItemId: '507f1f77bcf86cd799439015',
          name: 'Classic Burger',
          price: 250,
          quantity: 2,
          itemTotal: 500
        }
      ],
      pricing: {
        itemsSubtotal: 500,
        deliveryFee: 25,
        platformFee: 10,
        total: 535
      },
      currentStatus: 'placed',
      createdAt: new Date().toISOString()
    }
  });
});

// API endpoints summary
app.get(`${baseRoute}/endpoints`, (req, res) => {
  res.json({
    success: true,
    message: 'Food Delivery Platform API with Swagger Documentation',
    documentation: {
      interactive: `http://localhost:3000/api-docs`,
      json: `http://localhost:3000/api-docs.json`,
      description: 'Complete API documentation with interactive testing'
    },
    endpoints: {
      authentication: [
        'POST /auth/register - User registration',
        'POST /auth/login - User login',
        'POST /auth/verify-otp - OTP verification (use 123456)',
        'POST /auth/refresh-token - Token refresh'
      ],
      user: [
        'GET /user/restaurants - Restaurant discovery with geospatial search',
        'GET /user/profile - User profile',
        'GET /user/orders - Order history'
      ],
      orders: [
        'POST /orders - Place new order',
        'GET /orders/:orderId - Get order details',
        'PUT /orders/:orderId/status - Update order status',
        'DELETE /orders/:orderId - Cancel order'
      ]
    },
    testInstructions: {
      step1: 'Visit http://localhost:3000/api-docs for interactive documentation',
      step2: 'Try POST /auth/register to create a user',
      step3: 'Use POST /auth/verify-otp with OTP: 123456',
      step4: 'Use the returned token for authenticated requests',
      step5: 'Test GET /user/restaurants to see restaurant discovery',
      step6: 'Test POST /orders to place an order'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    documentation: 'Visit http://localhost:3000/api-docs for complete API documentation',
    availableEndpoints: `Visit http://localhost:3000${baseRoute}/endpoints for endpoint list`
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: error.message
  });
});

const PORT = process.env.PORT || 3000;

app.listen(PORT, () => {
  console.log('🎉 ===============================================');
  console.log('🚀 FOOD DELIVERY API WITH SWAGGER DOCUMENTATION');
  console.log('🎉 ===============================================');
  console.log(`✅ Server running on: http://localhost:${PORT}`);
  console.log(`📚 Swagger Documentation: http://localhost:${PORT}/api-docs`);
  console.log(`📋 API Endpoints: http://localhost:${PORT}${baseRoute}/endpoints`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
  console.log('');
  console.log('🎯 Features:');
  console.log('   • Interactive API Documentation with Swagger UI');
  console.log('   • Complete endpoint documentation');
  console.log('   • Request/Response examples');
  console.log('   • Authentication testing (use OTP: 123456)');
  console.log('   • Mock data for testing');
  console.log('');
  console.log('🚀 Ready for testing and integration!');
  console.log('🎉 ===============================================');
});
