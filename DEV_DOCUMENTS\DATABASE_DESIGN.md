# 🗄️ Food Delivery Platform - Database Design

## 📋 Overview

This document outlines the comprehensive database design for the franchise food delivery platform using a **hybrid approach**:

- **MongoDB**: Primary database for operational data (90% of operations)
- **PostgreSQL**: Secondary database for financial transactions and settlements

## 🏗️ Database Architecture

```
┌─────────────────────┐    ┌─────────────────────┐
│     MongoDB         │    │    PostgreSQL       │
│   (Primary DB)      │    │   (Financial DB)    │
├─────────────────────┤    ├─────────────────────┤
│ • Users             │    │ • Transactions      │
│ • Franchises        │    │ • Settlements       │
│ • Restaurants       │    │ • Commission Rules  │
│ • Orders            │    │ • Audit Logs        │
│ • Menus             │    │ • Tax Calculations  │
│ • Reviews           │    │ • Financial Reports │
│ • Real-time Data    │    │ • Payment Records   │
└─────────────────────┘    └─────────────────────┘
```

## 📊 MongoDB Collections (Primary Database)

### **Collection Naming Convention**
- Use plural nouns: `users`, `franchises`, `restaurants`, `orders`
- Use camelCase for field names: `firstName`, `deliveryAddress`
- Use snake_case for compound field names: `created_at`, `updated_at`

---

## 1️⃣ **users** Collection

### **Purpose**: Store all user types (customers, restaurant owners, delivery partners, admins)

### **Schema Structure**
```javascript
{
  _id: ObjectId("user_id"),
  email: "<EMAIL>",                    // Unique, required
  phone: "+************",                       // Unique per role, required
  role: "customer",                             // Enum: customer, restaurant_owner, delivery_partner, franchise_admin, super_admin
  franchiseId: ObjectId("franchise_id"),        // Required except for super_admin
  
  // Profile Information (varies by role)
  profile: {
    name: "John Doe",                           // Required, 2-100 chars
    avatar: "https://cdn.example.com/avatar.jpg",
    dateOfBirth: ISODate("1990-05-15"),
    gender: "male",                             // Enum: male, female, other
    
    // Customer-specific fields
    addresses: [{
      _id: ObjectId(),
      type: "home",                             // Enum: home, work, other
      address: "123 Main St, Mumbai",
      coordinates: [72.8200, 18.9700],          // [longitude, latitude]
      landmark: "Near Metro Station",
      isDefault: true
    }],
    preferences: {
      cuisines: ["indian", "chinese"],
      dietaryRestrictions: ["vegetarian"],
      spiceLevel: "medium"                      // Enum: mild, medium, hot
    },
    
    // Delivery Partner specific fields
    deliveryPartnerInfo: {
      vehicleType: "bike",                      // Enum: bike, scooter, bicycle, car
      vehicleNumber: "MH01AB1234",
      drivingLicense: "MH1420110012345",
      documents: {
        license: "url_to_license",
        rc: "url_to_rc", 
        insurance: "url_to_insurance",
        photo: "url_to_photo"
      },
      currentLocation: [72.8200, 18.9700],
      isOnline: false,
      rating: 4.5,                              // 0-5 scale
      totalDeliveries: 150
    },
    
    // Restaurant Owner specific fields
    restaurantInfo: {
      restaurantIds: [ObjectId("restaurant_id")]
    }
  },
  
  // Authentication & Security
  authentication: {
    passwordHash: "hashed_password",            // bcrypt hash, select: false
    emailVerified: true,
    phoneVerified: true,
    lastLogin: ISODate("2024-12-01"),
    resetPasswordToken: null,
    resetPasswordExpiry: null
  },
  
  // User Settings
  settings: {
    notifications: {
      push: true,
      sms: true,
      email: false
    },
    language: "en",                             // ISO language code
    currency: "INR"                             // ISO currency code
  },
  
  status: "active",                             // Enum: active, inactive, suspended, pending_verification
  createdAt: ISODate("2024-01-01"),
  updatedAt: ISODate("2024-12-01")
}
```

### **Indexes**
```javascript
// Unique indexes
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "phone": 1, "role": 1 }, { unique: true });

// Query optimization indexes
db.users.createIndex({ "franchiseId": 1 });
db.users.createIndex({ "role": 1 });
db.users.createIndex({ "status": 1 });
db.users.createIndex({ "profile.addresses.coordinates": "2dsphere" });
db.users.createIndex({ "profile.deliveryPartnerInfo.isOnline": 1 });
```

### **Validation Rules**
```javascript
db.createCollection("users", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["email", "phone", "role", "profile.name"],
      properties: {
        email: { 
          bsonType: "string", 
          pattern: "^.+@.+\..+$" 
        },
        phone: { 
          bsonType: "string", 
          pattern: "^\\+[1-9]\\d{1,14}$" 
        },
        role: { 
          enum: ["customer", "restaurant_owner", "delivery_partner", "franchise_admin", "super_admin"] 
        },
        status: { 
          enum: ["active", "inactive", "suspended", "pending_verification"] 
        }
      }
    }
  }
});
```

---

## 2️⃣ **franchises** Collection

### **Purpose**: Store franchise information and business settings

### **Schema Structure**
```javascript
{
  _id: ObjectId("franchise_id"),
  name: "Mumbai Central Franchise",
  slug: "mumbai-central",                       // Auto-generated, unique
  
  // Franchise Owner Information
  owner: {
    name: "John Doe",
    email: "<EMAIL>",                  // Unique
    phone: "+************",
    documents: {
      pan: "**********",
      gst: "27**********1Z5",
      bankAccount: {
        number: "**********",
        ifsc: "HDFC0001234",
        name: "John Doe"
      }
    }
  },
  
  // Location & Service Areas
  location: {
    address: "Mumbai Central, Mumbai, Maharashtra",
    coordinates: [72.8200, 18.9700],            // [longitude, latitude]
    serviceAreas: [{
      type: "Circle",                           // Enum: Circle, Polygon
      center: [72.8200, 18.9700],              // For Circle
      radius: 10000,                           // For Circle (meters)
      coordinates: [[[lng, lat], [lng, lat]]]  // For Polygon
    }],
    city: "Mumbai",
    state: "Maharashtra", 
    country: "India",
    pincode: "400008"
  },
  
  // Business Configuration
  businessSettings: {
    operatingHours: {
      monday: { open: "09:00", close: "23:00", isOpen: true },
      tuesday: { open: "09:00", close: "23:00", isOpen: true },
      wednesday: { open: "09:00", close: "23:00", isOpen: true },
      thursday: { open: "09:00", close: "23:00", isOpen: true },
      friday: { open: "09:00", close: "23:00", isOpen: true },
      saturday: { open: "09:00", close: "23:00", isOpen: true },
      sunday: { open: "09:00", close: "23:00", isOpen: true }
    },
    deliverySettings: {
      minOrderAmount: 150,                      // Minimum order value
      maxDeliveryDistance: 10,                 // km
      avgDeliveryTime: 35,                     // minutes
      deliveryFee: {
        base: 25,                              // Base delivery fee
        perKm: 5,                              // Per km charge
        freeDeliveryAbove: 300                 // Free delivery threshold
      }
    }
  },
  
  // Commission Structure
  commissionSettings: {
    restaurantCommission: 0.18,                 // 18%
    deliveryCommission: 0.15,                  // 15%
    platformFee: 0.02,                         // 2%
    paymentGatewayFee: 0.025                   // 2.5%
  },
  
  status: "active",                             // Enum: active, inactive, suspended
  onboardingDate: ISODate("2024-01-15"),
  lastActive: ISODate("2024-12-01"),
  createdAt: ISODate("2024-01-01"),
  updatedAt: ISODate("2024-12-01")
}
```

### **Indexes**
```javascript
db.franchises.createIndex({ "location.coordinates": "2dsphere" });
db.franchises.createIndex({ "slug": 1 }, { unique: true });
db.franchises.createIndex({ "owner.email": 1 }, { unique: true });
db.franchises.createIndex({ "status": 1 });
db.franchises.createIndex({ "location.city": 1, "status": 1 });
```

---

## 3️⃣ **restaurants** Collection

### **Purpose**: Store restaurant information, menus, and operational data

### **Schema Structure**
```javascript
{
  _id: ObjectId("restaurant_id"),
  name: "Burger Palace",
  slug: "burger-palace-mumbai",                 // Auto-generated, unique
  franchiseId: ObjectId("franchise_id"),        // Required
  ownerId: ObjectId("user_id"),                 // Restaurant owner
  
  // Business Information
  businessInfo: {
    type: "restaurant",                         // Enum: restaurant, cloud_kitchen, cafe
    cuisine: ["american", "fast_food"],         // Array of cuisine types
    description: "Best burgers in town",
    images: [
      "https://s3.aws.com/restaurant1.jpg",
      "https://s3.aws.com/restaurant2.jpg"
    ],
    license: {
      fssai: "**********1234",                  // 14-digit FSSAI number
      gst: "27**********1Z5"                    // GST number
    }
  },
  
  // Location & Delivery Zones
  location: {
    address: "Shop 123, ABC Mall, Mumbai",
    coordinates: [72.8200, 18.9700],
    landmark: "Near XYZ Metro",
    deliveryZones: [{
      type: "Circle",
      center: [72.8200, 18.9700],
      radius: 5000                              // 5km radius in meters
    }]
  },
  
  // Operational Information
  operationalInfo: {
    businessHours: {
      monday: { open: "10:00", close: "22:00", isOpen: true },
      tuesday: { open: "10:00", close: "22:00", isOpen: true },
      wednesday: { open: "10:00", close: "22:00", isOpen: true },
      thursday: { open: "10:00", close: "22:00", isOpen: true },
      friday: { open: "10:00", close: "22:00", isOpen: true },
      saturday: { open: "10:00", close: "22:00", isOpen: true },
      sunday: { open: "10:00", close: "22:00", isOpen: true }
    },
    avgPreparationTime: 25,                     // minutes
    isCurrentlyOpen: true,
    acceptingOrders: true,
    minOrderAmount: 100
  },

  // Menu Items
  menu: [{
    _id: ObjectId("menu_item_id"),
    name: "Classic Burger",
    description: "Juicy beef patty with fresh vegetables",
    category: "Burgers",
    price: 250,                                 // Base price
    originalPrice: 300,                         // Original price (for discounts)
    images: ["https://s3.aws.com/burger1.jpg"],
    isVegetarian: false,
    isVegan: false,
    spiceLevel: "mild",                         // Enum: mild, medium, hot
    ingredients: ["beef", "lettuce", "tomato", "onion"],
    allergens: ["gluten"],
    nutritionalInfo: {
      calories: 450,
      protein: 25,                              // grams
      carbs: 35,                                // grams
      fat: 22                                   // grams
    },
    preparationTime: 15,                        // minutes
    isAvailable: true,
    variants: [{
      name: "Regular",
      price: 250,
      isDefault: true
    }, {
      name: "Large",
      price: 300,
      isDefault: false
    }],
    addons: [{
      name: "Extra Cheese",
      price: 30,
      isAvailable: true
    }]
  }],

  // Ratings & Reviews
  ratings: {
    average: 4.2,                               // 0-5 scale
    count: 150,                                 // Total reviews
    breakdown: {
      5: 80,                                    // Count of 5-star reviews
      4: 50,
      3: 15,
      2: 3,
      1: 2
    }
  },

  // Financial Information
  financials: {
    commissionRate: 0.18,                       // 18%
    settlementCycle: "weekly",                  // Enum: daily, weekly, monthly
    bankDetails: {
      accountNumber: "**********",
      ifsc: "HDFC0001234",
      accountName: "Burger Palace"
    }
  },

  // Performance Metrics
  performance: {
    totalOrders: 1250,
    avgRating: 4.2,
    avgDeliveryTime: 35,                        // minutes
    acceptanceRate: 0.95,                       // 95%
    cancellationRate: 0.05                      // 5%
  },

  status: "active",                             // Enum: pending, active, inactive, suspended
  verificationStatus: "verified",               // Enum: pending, verified, rejected
  onboardingDate: ISODate("2024-02-01"),
  createdAt: ISODate("2024-01-15"),
  updatedAt: ISODate("2024-12-01")
}
```

### **Indexes**
```javascript
db.restaurants.createIndex({ "location.coordinates": "2dsphere" });
db.restaurants.createIndex({ "franchiseId": 1, "status": 1 });
db.restaurants.createIndex({ "slug": 1 }, { unique: true });
db.restaurants.createIndex({ "ownerId": 1 });
db.restaurants.createIndex({ "businessInfo.cuisine": 1 });
db.restaurants.createIndex({ "ratings.average": -1 });
db.restaurants.createIndex({ "operationalInfo.isCurrentlyOpen": 1 });
```

---

## 4️⃣ **orders** Collection

### **Purpose**: Store order information, tracking, and status updates

### **Schema Structure**
```javascript
{
  _id: ObjectId("order_id"),
  orderNumber: "ORD-2024-001234",               // Auto-generated, unique
  franchiseId: ObjectId("franchise_id"),
  customerId: ObjectId("user_id"),
  restaurantId: ObjectId("restaurant_id"),
  deliveryPartnerId: ObjectId("user_id"),       // Assigned when picked up

  // Order Items
  items: [{
    menuItemId: ObjectId("menu_item_id"),
    name: "Classic Burger",                     // Snapshot at order time
    price: 250,                                 // Price at order time
    quantity: 2,
    variants: ["Large"],
    addons: [{
      name: "Extra Cheese",
      price: 30,
      quantity: 1
    }],
    specialInstructions: "No onions please",
    itemTotal: 530                              // (250*2) + (30*1)
  }],

  // Pricing Breakdown
  pricing: {
    itemsSubtotal: 530,
    taxes: {
      cgst: 26.5,                               // 5%
      sgst: 26.5,                               // 5%
      total: 53
    },
    deliveryFee: 25,
    platformFee: 12,
    discounts: {
      couponCode: "FIRST20",
      discountAmount: 50,
      description: "20% off on first order"
    },
    total: 570                                  // Final amount
  },

  // Addresses
  addresses: {
    pickup: {
      restaurantName: "Burger Palace",
      address: "Shop 123, ABC Mall, Mumbai",
      coordinates: [72.8200, 18.9700],
      contactNumber: "+************"
    },
    delivery: {
      customerName: "John Doe",
      address: "456 Residential Complex, Mumbai",
      coordinates: [72.8300, 18.9800],
      contactNumber: "+919876543211",
      landmark: "Gate 2, Building A",
      deliveryInstructions: "Call when you arrive"
    }
  },

  // Order Timeline
  timeline: [{
    status: "placed",
    timestamp: ISODate("2024-12-01T10:00:00Z"),
    note: "Order placed by customer"
  }, {
    status: "accepted",
    timestamp: ISODate("2024-12-01T10:02:00Z"),
    note: "Order accepted by restaurant",
    acceptedBy: ObjectId("user_id")
  }, {
    status: "preparing",
    timestamp: ISODate("2024-12-01T10:05:00Z"),
    estimatedPreparationTime: 25
  }],

  currentStatus: "preparing",                   // Enum: placed, accepted, preparing, ready, picked_up, out_for_delivery, delivered, cancelled

  // Delivery Information
  delivery: {
    estimatedPickupTime: ISODate("2024-12-01T10:30:00Z"),
    estimatedDeliveryTime: ISODate("2024-12-01T11:00:00Z"),
    actualPickupTime: null,
    actualDeliveryTime: null,
    deliveryDistance: 3.5,                      // km
    deliveryRoute: [
      [72.8200, 18.9700],                       // Restaurant location
      [72.8250, 18.9750],                       // Waypoint
      [72.8300, 18.9800]                        // Customer location
    ],
    deliveryPartnerLocation: [72.8250, 18.9750], // Real-time location
    deliveryOTP: "1234"                         // 4-digit OTP for delivery verification
  },

  // Payment Information
  payment: {
    method: "online",                           // Enum: online, cod
    gateway: "razorpay",
    transactionId: "txn_**********",
    status: "completed",                        // Enum: pending, completed, failed, refunded
    paidAmount: 570,
    refundAmount: 0,
    paymentTimestamp: ISODate("2024-12-01T10:00:30Z")
  },

  // Customer Feedback
  feedback: {
    customerRating: 5,                          // 1-5 scale
    customerReview: "Excellent food and quick delivery!",
    restaurantRating: 5,
    deliveryRating: 4,
    reviewTimestamp: ISODate("2024-12-01T11:30:00Z")
  },

  createdAt: ISODate("2024-12-01T10:00:00Z"),
  updatedAt: ISODate("2024-12-01T11:00:00Z")
}
```

### **Indexes**
```javascript
db.orders.createIndex({ "orderNumber": 1 }, { unique: true });
db.orders.createIndex({ "customerId": 1, "createdAt": -1 });
db.orders.createIndex({ "restaurantId": 1, "currentStatus": 1 });
db.orders.createIndex({ "deliveryPartnerId": 1, "currentStatus": 1 });
db.orders.createIndex({ "franchiseId": 1, "createdAt": -1 });
db.orders.createIndex({ "currentStatus": 1 });
db.orders.createIndex({ "payment.status": 1 });
```

---

## 5️⃣ **reviews** Collection

### **Purpose**: Store detailed customer reviews and ratings

### **Schema Structure**
```javascript
{
  _id: ObjectId("review_id"),
  orderId: ObjectId("order_id"),
  customerId: ObjectId("user_id"),
  restaurantId: ObjectId("restaurant_id"),
  deliveryPartnerId: ObjectId("user_id"),       // Optional

  ratings: {
    overall: 5,                                 // 1-5 scale
    food: 5,
    service: 4,
    delivery: 4,
    value: 5
  },

  review: {
    title: "Excellent Experience!",
    comment: "Food was delicious and delivery was quick.",
    images: ["https://s3.aws.com/review1.jpg"], // Optional photos
    isVerifiedPurchase: true
  },

  response: {                                   // Restaurant response
    comment: "Thank you for your feedback!",
    respondedBy: ObjectId("user_id"),
    respondedAt: ISODate("2024-12-01T12:00:00Z")
  },

  status: "published",                          // Enum: published, hidden, flagged
  helpfulVotes: 5,                              // Number of helpful votes
  createdAt: ISODate("2024-12-01T11:30:00Z"),
  updatedAt: ISODate("2024-12-01T12:00:00Z")
}
```

### **Indexes**
```javascript
db.reviews.createIndex({ "restaurantId": 1, "status": 1, "createdAt": -1 });
db.reviews.createIndex({ "customerId": 1, "createdAt": -1 });
db.reviews.createIndex({ "orderId": 1 }, { unique: true });
db.reviews.createIndex({ "ratings.overall": -1 });
```

---

## 6️⃣ **coupons** Collection

### **Purpose**: Store discount coupons and promotional codes

### **Schema Structure**
```javascript
{
  _id: ObjectId("coupon_id"),
  code: "FIRST20",                              // Unique coupon code
  title: "First Order Discount",
  description: "20% off on your first order",

  discountType: "percentage",                   // Enum: percentage, fixed, free_delivery
  discountValue: 20,                            // 20% or fixed amount
  maxDiscountAmount: 100,                       // Maximum discount cap

  conditions: {
    minOrderAmount: 200,                        // Minimum order value
    maxUsagePerUser: 1,                         // Usage limit per user
    totalUsageLimit: 1000,                      // Total usage limit
    applicableUserTypes: ["new_customer"],      // Enum: new_customer, all, premium
    applicableRestaurants: [],                  // Empty = all restaurants
    applicableFranchises: [ObjectId("franchise_id")]
  },

  validity: {
    startDate: ISODate("2024-01-01"),
    endDate: ISODate("2024-12-31"),
    isActive: true
  },

  usage: {
    totalUsed: 250,
    usedBy: [ObjectId("user_id")]               // Track users who used it
  },

  createdBy: ObjectId("user_id"),               // Admin who created
  createdAt: ISODate("2024-01-01"),
  updatedAt: ISODate("2024-06-01")
}
```

### **Indexes**
```javascript
db.coupons.createIndex({ "code": 1 }, { unique: true });
db.coupons.createIndex({ "validity.isActive": 1, "validity.endDate": 1 });
db.coupons.createIndex({ "conditions.applicableFranchises": 1 });
```

---

## 🐘 PostgreSQL Tables (Financial Database)

### **Purpose**: Handle financial transactions, settlements, and audit trails

## 1️⃣ **transactions** Table

### **Purpose**: Store all financial transactions

```sql
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id VARCHAR(24) NOT NULL,              -- MongoDB ObjectId
    franchise_id VARCHAR(24) NOT NULL,
    transaction_type VARCHAR(30) NOT NULL CHECK (
        transaction_type IN (
            'order_payment',
            'commission_settlement',
            'refund',
            'adjustment',
            'delivery_fee',
            'platform_fee'
        )
    ),
    amount DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    payment_gateway VARCHAR(20),                 -- razorpay, stripe, paytm
    gateway_transaction_id VARCHAR(100),
    gateway_response JSONB,                      -- Full gateway response
    status VARCHAR(20) NOT NULL CHECK (
        status IN ('pending', 'completed', 'failed', 'cancelled', 'refunded')
    ),
    metadata JSONB,                              -- Additional transaction data
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_transactions_order_id ON transactions(order_id);
CREATE INDEX idx_transactions_franchise_id ON transactions(franchise_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_type ON transactions(transaction_type);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
CREATE INDEX idx_transactions_gateway ON transactions(payment_gateway, gateway_transaction_id);
```

## 2️⃣ **commission_settlements** Table

### **Purpose**: Store commission settlement records

```sql
CREATE TABLE commission_settlements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    franchise_id VARCHAR(24) NOT NULL,
    restaurant_id VARCHAR(24),                  -- NULL for delivery partner settlements
    delivery_partner_id VARCHAR(24),            -- NULL for restaurant settlements
    settlement_type VARCHAR(20) NOT NULL CHECK (
        settlement_type IN ('restaurant', 'delivery_partner', 'franchise')
    ),
    settlement_period_start DATE NOT NULL,
    settlement_period_end DATE NOT NULL,

    -- Financial Summary
    total_orders INTEGER NOT NULL DEFAULT 0,
    gross_revenue DECIMAL(15,2) NOT NULL DEFAULT 0,
    commission_amount DECIMAL(12,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    adjustment_amount DECIMAL(10,2) DEFAULT 0,   -- Manual adjustments
    net_settlement DECIMAL(12,2) NOT NULL DEFAULT 0,

    -- Payment Details
    payment_method VARCHAR(20) DEFAULT 'bank_transfer',
    bank_reference_number VARCHAR(50),
    utr_number VARCHAR(50),                      -- Unique Transaction Reference

    status VARCHAR(20) DEFAULT 'pending' CHECK (
        status IN ('pending', 'processed', 'completed', 'failed', 'disputed')
    ),

    -- Metadata
    settlement_details JSONB,                    -- Detailed breakdown
    notes TEXT,
    processed_by VARCHAR(24),                    -- Admin user ID
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_settlements_franchise_id ON commission_settlements(franchise_id);
CREATE INDEX idx_settlements_restaurant_id ON commission_settlements(restaurant_id);
CREATE INDEX idx_settlements_delivery_partner_id ON commission_settlements(delivery_partner_id);
CREATE INDEX idx_settlements_period ON commission_settlements(settlement_period_start, settlement_period_end);
CREATE INDEX idx_settlements_status ON commission_settlements(status);
CREATE INDEX idx_settlements_type ON commission_settlements(settlement_type);
```

## 3️⃣ **audit_logs** Table

### **Purpose**: Store audit trail for all critical operations

```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_type VARCHAR(20) NOT NULL,           -- user, order, restaurant, franchise
    entity_id VARCHAR(24) NOT NULL,             -- MongoDB ObjectId
    action VARCHAR(30) NOT NULL,                -- create, update, delete, status_change
    performed_by VARCHAR(24) NOT NULL,          -- User ID who performed action
    ip_address INET,
    user_agent TEXT,

    -- Change Details
    old_values JSONB,                           -- Previous state
    new_values JSONB,                           -- New state
    changes JSONB,                              -- Specific fields changed

    -- Context
    reason TEXT,                                -- Reason for change
    metadata JSONB,                             -- Additional context

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_audit_logs_entity ON audit_logs(entity_type, entity_id);
CREATE INDEX idx_audit_logs_performed_by ON audit_logs(performed_by);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
```

---

## 🔗 **Relationships & Data Integrity**

### **MongoDB Relationships**
```javascript
// User -> Franchise (Many-to-One)
users.franchiseId -> franchises._id

// Restaurant -> Franchise (Many-to-One)
restaurants.franchiseId -> franchises._id

// Restaurant -> User (Many-to-One)
restaurants.ownerId -> users._id

// Order -> User (Many-to-One)
orders.customerId -> users._id
orders.deliveryPartnerId -> users._id

// Order -> Restaurant (Many-to-One)
orders.restaurantId -> restaurants._id

// Order -> Franchise (Many-to-One)
orders.franchiseId -> franchises._id

// Review -> Order (One-to-One)
reviews.orderId -> orders._id
```

### **Cross-Database Relationships**
```javascript
// PostgreSQL -> MongoDB
transactions.order_id -> orders._id (MongoDB)
transactions.franchise_id -> franchises._id (MongoDB)
commission_settlements.franchise_id -> franchises._id (MongoDB)
commission_settlements.restaurant_id -> restaurants._id (MongoDB)
audit_logs.entity_id -> [various MongoDB collections]._id
```

---

## 📊 **Data Validation Rules**

### **MongoDB Validation**
```javascript
// Email validation
email: {
    bsonType: "string",
    pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
}

// Phone validation (International format)
phone: {
    bsonType: "string",
    pattern: "^\\+[1-9]\\d{1,14}$"
}

// Coordinates validation
coordinates: {
    bsonType: "array",
    minItems: 2,
    maxItems: 2,
    items: {
        bsonType: "double",
        minimum: -180,
        maximum: 180
    }
}

// Price validation
price: {
    bsonType: "number",
    minimum: 0,
    maximum: 99999.99
}
```

### **PostgreSQL Constraints**
```sql
-- Amount validation
ALTER TABLE transactions ADD CONSTRAINT chk_positive_amount
CHECK (amount >= 0);

-- Date validation
ALTER TABLE commission_settlements ADD CONSTRAINT chk_valid_period
CHECK (settlement_period_end >= settlement_period_start);

-- Settlement calculation validation
ALTER TABLE commission_settlements ADD CONSTRAINT chk_settlement_calculation
CHECK (net_settlement = gross_revenue - commission_amount - tax_amount + COALESCE(adjustment_amount, 0));
```

---

## 🚀 **Database Setup & Configuration**

### **MongoDB Setup**
```bash
# 1. Install MongoDB (Local or use MongoDB Atlas)
# For local installation:
# Download from https://www.mongodb.com/try/download/community

# 2. Start MongoDB service
mongod --dbpath /data/db

# 3. Connect to MongoDB
mongosh

# 4. Create database and collections
use food_delivery_platform

# 5. Create collections with validation
db.createCollection("users", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["email", "phone", "role", "profile"],
      properties: {
        email: { bsonType: "string", pattern: "^.+@.+\..+$" },
        phone: { bsonType: "string", pattern: "^\\+[1-9]\\d{1,14}$" },
        role: { enum: ["customer", "restaurant_owner", "delivery_partner", "franchise_admin", "super_admin"] },
        status: { enum: ["active", "inactive", "suspended", "pending_verification"] }
      }
    }
  }
});

# 6. Create indexes for performance
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "phone": 1, "role": 1 }, { unique: true });
db.users.createIndex({ "franchiseId": 1 });
db.users.createIndex({ "role": 1 });
db.users.createIndex({ "profile.addresses.coordinates": "2dsphere" });

# Repeat for other collections...
```

### **PostgreSQL Setup**
```sql
-- 1. Install PostgreSQL
-- Download from https://www.postgresql.org/download/

-- 2. Create database
CREATE DATABASE food_delivery_financial;

-- 3. Connect to database
\c food_delivery_financial;

-- 4. Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 5. Create tables (as defined above)
-- 6. Create indexes
-- 7. Set up constraints
```

### **Environment Configuration**
```bash
# .env file
MONGODB_URI=mongodb://localhost:27017/food_delivery_platform
POSTGRESQL_URI=postgresql://username:password@localhost:5432/food_delivery_financial

# For production, use connection pooling:
MONGODB_MAX_POOL_SIZE=10
POSTGRESQL_MAX_POOL_SIZE=20
```

---

## ⚡ **Performance Optimization**

### **MongoDB Optimization**
```javascript
// 1. Compound Indexes for common queries
db.orders.createIndex({
  "restaurantId": 1,
  "currentStatus": 1,
  "createdAt": -1
});

// 2. Partial Indexes for specific conditions
db.users.createIndex(
  { "profile.deliveryPartnerInfo.isOnline": 1 },
  { partialFilterExpression: { "role": "delivery_partner" } }
);

// 3. Text Indexes for search
db.restaurants.createIndex({
  "name": "text",
  "businessInfo.description": "text",
  "businessInfo.cuisine": "text"
});

// 4. TTL Indexes for temporary data
db.otp_sessions.createIndex(
  { "createdAt": 1 },
  { expireAfterSeconds: 300 }  // 5 minutes
);
```

### **PostgreSQL Optimization**
```sql
-- 1. Partitioning for large tables
CREATE TABLE transactions_2024 PARTITION OF transactions
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

-- 2. Materialized Views for reporting
CREATE MATERIALIZED VIEW daily_revenue AS
SELECT
    DATE(created_at) as date,
    franchise_id,
    SUM(amount) as total_revenue,
    COUNT(*) as total_transactions
FROM transactions
WHERE transaction_type = 'order_payment'
GROUP BY DATE(created_at), franchise_id;

-- 3. Refresh materialized view periodically
REFRESH MATERIALIZED VIEW daily_revenue;
```

---

## 🔒 **Security Best Practices**

### **Data Protection**
```javascript
// 1. Sensitive field exclusion
const userSchema = new mongoose.Schema({
  authentication: {
    passwordHash: {
      type: String,
      required: true,
      select: false  // Never include in queries by default
    }
  }
});

// 2. Field-level encryption for PII
const encryptedFields = ['phone', 'email', 'bankAccount'];

// 3. Role-based field access
const getFieldsForRole = (role) => {
  const baseFields = ['name', 'email', 'role'];
  const roleFields = {
    customer: [...baseFields, 'addresses', 'preferences'],
    restaurant_owner: [...baseFields, 'restaurantInfo'],
    delivery_partner: [...baseFields, 'deliveryPartnerInfo']
  };
  return roleFields[role] || baseFields;
};
```

### **Database Security**
```sql
-- 1. Row Level Security (PostgreSQL)
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

CREATE POLICY franchise_isolation ON transactions
FOR ALL TO app_user
USING (franchise_id = current_setting('app.current_franchise_id'));

-- 2. Audit triggers
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_logs (
        entity_type, entity_id, action,
        old_values, new_values, performed_by
    ) VALUES (
        TG_TABLE_NAME, NEW.id, TG_OP,
        row_to_json(OLD), row_to_json(NEW),
        current_setting('app.current_user_id')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

---

## 📈 **Monitoring & Analytics**

### **Key Metrics to Track**
```javascript
// 1. Database Performance Metrics
const dbMetrics = {
  mongodb: {
    connectionPoolSize: 'db.serverStatus().connections',
    queryExecutionTime: 'db.collection.explain("executionStats")',
    indexUsage: 'db.collection.aggregate([{$indexStats: {}}])'
  },
  postgresql: {
    connectionCount: 'SELECT count(*) FROM pg_stat_activity',
    slowQueries: 'SELECT * FROM pg_stat_statements ORDER BY total_time DESC',
    indexUsage: 'SELECT * FROM pg_stat_user_indexes'
  }
};

// 2. Business Metrics
const businessMetrics = {
  orders: {
    totalOrders: 'db.orders.countDocuments()',
    ordersByStatus: 'db.orders.aggregate([{$group: {_id: "$currentStatus", count: {$sum: 1}}}])',
    avgOrderValue: 'db.orders.aggregate([{$group: {_id: null, avg: {$avg: "$pricing.total"}}}])'
  },
  restaurants: {
    activeRestaurants: 'db.restaurants.countDocuments({status: "active"})',
    avgRating: 'db.restaurants.aggregate([{$group: {_id: null, avg: {$avg: "$ratings.average"}}}])'
  }
};
```

---

## 🔄 **Data Migration & Backup**

### **Backup Strategy**
```bash
# MongoDB Backup
mongodump --uri="mongodb://localhost:27017/food_delivery_platform" --out=/backup/mongodb/

# PostgreSQL Backup
pg_dump food_delivery_financial > /backup/postgresql/backup.sql

# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mongodump --uri="$MONGODB_URI" --out="/backup/mongodb/$DATE/"
pg_dump "$POSTGRESQL_DB" > "/backup/postgresql/backup_$DATE.sql"
```

### **Data Migration**
```javascript
// Migration script example
const migrationV1toV2 = async () => {
  // Add new field to existing documents
  await db.users.updateMany(
    { "profile.preferences": { $exists: false } },
    { $set: { "profile.preferences": { cuisines: [], dietaryRestrictions: [] } } }
  );

  // Create new indexes
  await db.users.createIndex({ "profile.preferences.cuisines": 1 });
};
```

---

## 📚 **Summary**

This database design provides:

✅ **Scalable Architecture**: Hybrid MongoDB + PostgreSQL approach
✅ **Comprehensive Schema**: All entities with proper relationships
✅ **Performance Optimized**: Strategic indexing and query optimization
✅ **Security Focused**: Data protection and access control
✅ **Audit Ready**: Complete audit trail for compliance
✅ **Production Ready**: Backup, monitoring, and migration strategies

The design supports the complete franchise food delivery ecosystem with room for future expansion and optimization.
```
