const EventEmitter = require('events');
const socketService = require('./socketService');
const logger = require('../utils/logger');

class RealtimeEventService extends EventEmitter {
  constructor() {
    super();
    this.setupEventListeners();
  }

  setupEventListeners() {
    // Order Events
    this.on('order:created', this.handleOrderCreated.bind(this));
    this.on('order:status-updated', this.handleOrderStatusUpdated.bind(this));
    this.on('order:cancelled', this.handleOrderCancelled.bind(this));
    this.on('order:assigned', this.handleOrderAssigned.bind(this));

    // Delivery Events
    this.on('delivery:location-updated', this.handleDeliveryLocationUpdated.bind(this));
    this.on('delivery:partner-assigned', this.handleDeliveryPartnerAssigned.bind(this));
    this.on('delivery:completed', this.handleDeliveryCompleted.bind(this));

    // Restaurant Events
    this.on('restaurant:status-changed', this.handleRestaurantStatusChanged.bind(this));
    this.on('restaurant:menu-updated', this.handleRestaurantMenuUpdated.bind(this));

    // Payment Events
    this.on('payment:completed', this.handlePaymentCompleted.bind(this));
    this.on('payment:failed', this.handlePaymentFailed.bind(this));

    // Notification Events
    this.on('notification:send', this.handleNotificationSend.bind(this));
  }

  // Order Event Handlers
  handleOrderCreated(orderData) {
    logger.info(`Real-time: Order created ${orderData.orderNumber}`);
    
    // Broadcast to restaurant
    socketService.broadcastNewOrder(orderData);
    
    // Send notification to customer
    this.sendNotificationToUser(orderData.customerId, {
      type: 'order_placed',
      title: 'Order Placed Successfully!',
      message: `Your order #${orderData.orderNumber} has been placed and will be prepared soon.`,
      orderId: orderData._id,
      data: { orderNumber: orderData.orderNumber }
    });

    // Send notification to restaurant
    this.sendNotificationToRestaurant(orderData.restaurantId, {
      type: 'new_order',
      title: 'New Order Received!',
      message: `New order #${orderData.orderNumber} for ₹${orderData.pricing.total}`,
      orderId: orderData._id,
      sound: 'order_alert.mp3'
    });
  }

  handleOrderStatusUpdated(orderData) {
    logger.info(`Real-time: Order status updated ${orderData.orderNumber} -> ${orderData.currentStatus}`);
    
    // Broadcast status update to all relevant parties
    socketService.broadcastOrderUpdate(orderData._id, orderData);
    
    // Send status-specific notifications
    this.sendOrderStatusNotifications(orderData);
  }

  handleOrderCancelled(orderData) {
    logger.info(`Real-time: Order cancelled ${orderData.orderNumber}`);
    
    // Broadcast cancellation
    socketService.broadcastOrderUpdate(orderData._id, orderData);
    
    // Notify restaurant
    this.sendNotificationToRestaurant(orderData.restaurantId, {
      type: 'order_cancelled',
      title: 'Order Cancelled',
      message: `Order #${orderData.orderNumber} has been cancelled by customer`,
      orderId: orderData._id
    });

    // Notify delivery partner if assigned
    if (orderData.deliveryPartnerId) {
      this.sendNotificationToUser(orderData.deliveryPartnerId, {
        type: 'order_cancelled',
        title: 'Order Cancelled',
        message: `Order #${orderData.orderNumber} has been cancelled`,
        orderId: orderData._id
      });
    }
  }

  handleOrderAssigned(orderData) {
    logger.info(`Real-time: Order assigned ${orderData.orderNumber} to ${orderData.deliveryPartnerId}`);
    
    // Notify delivery partner
    this.sendNotificationToUser(orderData.deliveryPartnerId, {
      type: 'order_assigned',
      title: 'New Delivery Assignment!',
      message: `You have been assigned order #${orderData.orderNumber}`,
      orderId: orderData._id,
      sound: 'assignment_alert.mp3',
      data: {
        pickupAddress: orderData.addresses.pickup.address,
        deliveryAddress: orderData.addresses.delivery.address,
        estimatedEarnings: orderData.delivery.partnerEarnings
      }
    });

    // Notify customer
    this.sendNotificationToUser(orderData.customerId, {
      type: 'delivery_partner_assigned',
      title: 'Delivery Partner Assigned',
      message: `Your order is now with our delivery partner and will be delivered soon!`,
      orderId: orderData._id
    });
  }

  // Delivery Event Handlers
  handleDeliveryLocationUpdated(data) {
    const { orderId, deliveryPartnerId, location } = data;
    
    // Broadcast location update to customer
    socketService.broadcastToOrder(orderId, 'delivery-location-update', {
      deliveryPartnerId,
      location,
      timestamp: new Date().toISOString()
    });
  }

  handleDeliveryPartnerAssigned(data) {
    const { orderId, deliveryPartnerId, partnerInfo } = data;
    
    // Broadcast assignment to customer
    socketService.broadcastToOrder(orderId, 'delivery-partner-assigned', {
      deliveryPartnerId,
      partnerInfo,
      timestamp: new Date().toISOString()
    });
  }

  handleDeliveryCompleted(orderData) {
    logger.info(`Real-time: Delivery completed ${orderData.orderNumber}`);
    
    // Send completion notification to customer
    this.sendNotificationToUser(orderData.customerId, {
      type: 'order_delivered',
      title: 'Order Delivered!',
      message: `Your order #${orderData.orderNumber} has been delivered successfully. Enjoy your meal!`,
      orderId: orderData._id,
      data: {
        deliveryTime: orderData.delivery.actualDeliveryTime,
        rating: {
          enabled: true,
          restaurantId: orderData.restaurantId,
          deliveryPartnerId: orderData.deliveryPartnerId
        }
      }
    });
  }

  // Restaurant Event Handlers
  handleRestaurantStatusChanged(data) {
    const { restaurantId, isOpen, acceptingOrders } = data;
    
    // Broadcast to all connected clients
    socketService.broadcastToRestaurant(restaurantId, 'status-changed', {
      isOpen,
      acceptingOrders,
      timestamp: new Date().toISOString()
    });
  }

  handleRestaurantMenuUpdated(data) {
    const { restaurantId, menuUpdates } = data;
    
    // Broadcast menu updates to customers viewing the restaurant
    socketService.broadcastToRestaurant(restaurantId, 'menu-updated', {
      menuUpdates,
      timestamp: new Date().toISOString()
    });
  }

  // Payment Event Handlers
  handlePaymentCompleted(data) {
    const { orderId, paymentData } = data;
    
    // Broadcast payment confirmation
    socketService.broadcastToOrder(orderId, 'payment-completed', {
      paymentData,
      timestamp: new Date().toISOString()
    });
  }

  handlePaymentFailed(data) {
    const { orderId, error } = data;
    
    // Broadcast payment failure
    socketService.broadcastToOrder(orderId, 'payment-failed', {
      error,
      timestamp: new Date().toISOString()
    });
  }

  // Notification Handler
  handleNotificationSend(data) {
    const { userId, notification } = data;
    
    // Send notification via socket
    socketService.broadcastToUser(userId, 'notification', notification);
    
    // Here you would also integrate with push notification services
    // this.sendPushNotification(userId, notification);
  }

  // Helper Methods
  sendOrderStatusNotifications(orderData) {
    const { currentStatus, customerId, restaurantId, deliveryPartnerId } = orderData;
    
    const statusMessages = {
      'accepted': {
        customer: 'Your order has been accepted and is being prepared!',
        title: 'Order Accepted'
      },
      'preparing': {
        customer: 'Your order is being prepared with love!',
        title: 'Order Being Prepared'
      },
      'ready': {
        customer: 'Your order is ready for pickup!',
        delivery: 'Order is ready for pickup at the restaurant',
        title: 'Order Ready'
      },
      'picked_up': {
        customer: 'Your order has been picked up and is on the way!',
        title: 'Order Picked Up'
      },
      'out_for_delivery': {
        customer: 'Your order is out for delivery!',
        title: 'Out for Delivery'
      },
      'delivered': {
        customer: 'Your order has been delivered! Enjoy your meal!',
        title: 'Order Delivered'
      }
    };

    const messages = statusMessages[currentStatus];
    if (!messages) return;

    // Send to customer
    if (messages.customer) {
      this.sendNotificationToUser(customerId, {
        type: `order_${currentStatus}`,
        title: messages.title,
        message: messages.customer,
        orderId: orderData._id
      });
    }

    // Send to delivery partner
    if (messages.delivery && deliveryPartnerId) {
      this.sendNotificationToUser(deliveryPartnerId, {
        type: `order_${currentStatus}`,
        title: messages.title,
        message: messages.delivery,
        orderId: orderData._id
      });
    }
  }

  sendNotificationToUser(userId, notification) {
    this.emit('notification:send', { userId, notification });
  }

  sendNotificationToRestaurant(restaurantId, notification) {
    socketService.broadcastToRestaurant(restaurantId, 'notification', notification);
  }

  // Public API for triggering events
  emitOrderCreated(orderData) {
    this.emit('order:created', orderData);
  }

  emitOrderStatusUpdated(orderData) {
    this.emit('order:status-updated', orderData);
  }

  emitOrderCancelled(orderData) {
    this.emit('order:cancelled', orderData);
  }

  emitDeliveryLocationUpdated(data) {
    this.emit('delivery:location-updated', data);
  }

  emitRestaurantStatusChanged(data) {
    this.emit('restaurant:status-changed', data);
  }

  emitPaymentCompleted(data) {
    this.emit('payment:completed', data);
  }
}

module.exports = new RealtimeEventService();
