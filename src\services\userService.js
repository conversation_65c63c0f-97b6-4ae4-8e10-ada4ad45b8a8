const User = require('../models/User');
const Order = require('../models/Order');
const logger = require('../utils/logger');
const { calculateDistance, isRestaurantOpen, calculateEstimatedDeliveryTime } = require('../utils/helpers');

class UserService {
  async getUserProfile(userId) {
    const user = await User.findById(userId)
      .populate('franchiseId', 'name location.city');

    if (!user) {
      throw new Error('User not found');
    }

    // Get user statistics
    const [totalOrders, totalSpent, favoriteRestaurants] = await Promise.all([
      Order.countDocuments({ customerId: user._id }),
      Order.aggregate([
        {
          $match: {
            customerId: user._id,
            currentStatus: { $in: ['delivered', 'completed'] }
          }
        },
        {
          $group: {
            _id: null,
            totalSpent: { $sum: '$pricing.total' }
          }
        }
      ]),
      Order.aggregate([
        { $match: { customerId: user._id } },
        { $group: { _id: '$restaurantId', orderCount: { $sum: 1 } } },
        { $sort: { orderCount: -1 } },
        { $limit: 5 },
        {
          $lookup: {
            from: 'restaurants',
            localField: '_id',
            foreignField: '_id',
            as: 'restaurant'
          }
        },
        { $unwind: '$restaurant' },
        {
          $project: {
            _id: '$restaurant._id',
            name: '$restaurant.name',
            cuisine: '$restaurant.businessInfo.cuisine',
            orderCount: 1
          }
        }
      ])
    ]);

    return {
      user,
      stats: {
        totalOrders,
        totalSpent: totalSpent[0]?.totalSpent || 0,
        favoriteRestaurants
      }
    };
  }

  async updateUserProfile(userId, updateData) {
    const { profile, settings } = updateData;
    const updateFields = {};

    if (profile) {
      Object.keys(profile).forEach(key => {
        if (key !== 'addresses') { // Handle addresses separately
          updateFields[`profile.${key}`] = profile[key];
        }
      });
    }

    if (settings) {
      Object.keys(settings).forEach(key => {
        updateFields[`settings.${key}`] = settings[key];
      });
    }

    const user = await User.findByIdAndUpdate(
      userId,
      { $set: updateFields },
      { new: true, runValidators: true }
    );

    if (!user) {
      throw new Error('User not found');
    }

    logger.info(`Profile updated for user: ${userId}`);

    return user;
  }

  async getUserAddresses(userId) {
    const user = await User.findById(userId).select('profile.addresses');
    
    if (!user) {
      throw new Error('User not found');
    }

    return user.profile.addresses || [];
  }

  async addUserAddress(userId, addressData) {
    const { type, address, coordinates, landmark, isDefault } = addressData;

    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // If this is set as default, unset other default addresses
    if (isDefault) {
      user.profile.addresses.forEach(addr => {
        addr.isDefault = false;
      });
    }

    // Add new address
    user.profile.addresses.push({
      type,
      address,
      coordinates,
      landmark,
      isDefault: isDefault || user.profile.addresses.length === 0 // First address is default
    });

    await user.save();

    logger.info(`Address added for user: ${userId}`);

    return user.profile.addresses[user.profile.addresses.length - 1];
  }

  async updateUserAddress(userId, addressId, updateData) {
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const address = user.profile.addresses.id(addressId);
    if (!address) {
      throw new Error('Address not found');
    }

    // If setting as default, unset other default addresses
    if (updateData.isDefault) {
      user.profile.addresses.forEach(addr => {
        if (addr._id.toString() !== addressId) {
          addr.isDefault = false;
        }
      });
    }

    // Update address fields
    Object.keys(updateData).forEach(key => {
      address[key] = updateData[key];
    });

    await user.save();

    logger.info(`Address updated for user: ${userId}`);

    return address;
  }

  async deleteUserAddress(userId, addressId) {
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const address = user.profile.addresses.id(addressId);
    if (!address) {
      throw new Error('Address not found');
    }

    const wasDefault = address.isDefault;
    user.profile.addresses.pull(addressId);

    // If deleted address was default, make first remaining address default
    if (wasDefault && user.profile.addresses.length > 0) {
      user.profile.addresses[0].isDefault = true;
    }

    await user.save();

    logger.info(`Address deleted for user: ${userId}`);

    return { message: 'Address deleted successfully' };
  }

  async getUserOrders(userId, filters = {}) {
    const { page = 1, limit = 10, status } = filters;

    let query = { customerId: userId };

    if (status) {
      query.currentStatus = status;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [orders, total] = await Promise.all([
      Order.find(query)
        .populate('restaurantId', 'name businessInfo.cuisine location.address')
        .populate('deliveryPartnerId', 'profile.name phone')
        .skip(skip)
        .limit(parseInt(limit))
        .sort({ createdAt: -1 }),
      Order.countDocuments(query)
    ]);

    return {
      orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    };
  }

  async getUserOrderHistory(userId, filters = {}) {
    const { startDate, endDate, restaurantId } = filters;
    
    let matchCondition = { customerId: userId };

    if (startDate || endDate) {
      matchCondition.createdAt = {};
      if (startDate) matchCondition.createdAt.$gte = new Date(startDate);
      if (endDate) matchCondition.createdAt.$lte = new Date(endDate);
    }

    if (restaurantId) {
      matchCondition.restaurantId = restaurantId;
    }

    const orderHistory = await Order.aggregate([
      { $match: matchCondition },
      {
        $lookup: {
          from: 'restaurants',
          localField: 'restaurantId',
          foreignField: '_id',
          as: 'restaurant'
        }
      },
      { $unwind: '$restaurant' },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          totalOrders: { $sum: 1 },
          totalSpent: { $sum: '$pricing.total' },
          restaurants: { $addToSet: '$restaurant.name' },
          avgOrderValue: { $avg: '$pricing.total' }
        }
      },
      { $sort: { '_id.year': -1, '_id.month': -1 } }
    ]);

    return orderHistory.map(item => ({
      period: `${item._id.year}-${String(item._id.month).padStart(2, '0')}`,
      totalOrders: item.totalOrders,
      totalSpent: Math.round(item.totalSpent * 100) / 100,
      avgOrderValue: Math.round(item.avgOrderValue * 100) / 100,
      uniqueRestaurants: item.restaurants.length
    }));
  }

  async getUserPreferences(userId) {
    const user = await User.findById(userId).select('settings preferences');
    
    if (!user) {
      throw new Error('User not found');
    }

    // Get user's order history to suggest preferences
    const orderHistory = await Order.aggregate([
      { $match: { customerId: userId } },
      {
        $lookup: {
          from: 'restaurants',
          localField: 'restaurantId',
          foreignField: '_id',
          as: 'restaurant'
        }
      },
      { $unwind: '$restaurant' },
      { $unwind: '$restaurant.businessInfo.cuisine' },
      {
        $group: {
          _id: '$restaurant.businessInfo.cuisine',
          orderCount: { $sum: 1 }
        }
      },
      { $sort: { orderCount: -1 } },
      { $limit: 5 }
    ]);

    return {
      settings: user.settings || {},
      preferences: user.preferences || {},
      suggestedCuisines: orderHistory.map(item => ({
        cuisine: item._id,
        orderCount: item.orderCount
      }))
    };
  }

  async updateUserPreferences(userId, preferences) {
    const user = await User.findByIdAndUpdate(
      userId,
      { $set: { preferences } },
      { new: true, runValidators: true }
    );

    if (!user) {
      throw new Error('User not found');
    }

    logger.info(`Preferences updated for user: ${userId}`);

    return user.preferences;
  }

  async getUserRecommendations(userId, userLocation = null) {
    const user = await User.findById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // Get user's order history to understand preferences
    const orderHistory = await Order.aggregate([
      { $match: { customerId: userId } },
      {
        $lookup: {
          from: 'restaurants',
          localField: 'restaurantId',
          foreignField: '_id',
          as: 'restaurant'
        }
      },
      { $unwind: '$restaurant' },
      {
        $group: {
          _id: '$restaurantId',
          orderCount: { $sum: 1 },
          restaurant: { $first: '$restaurant' }
        }
      },
      { $sort: { orderCount: -1 } }
    ]);

    // Extract preferred cuisines
    const preferredCuisines = [...new Set(
      orderHistory.flatMap(item => item.restaurant.businessInfo.cuisine)
    )].slice(0, 3);

    // Find similar restaurants
    let recommendationQuery = {
      status: 'active',
      verificationStatus: 'verified',
      _id: { $nin: orderHistory.map(item => item._id) } // Exclude already ordered restaurants
    };

    if (preferredCuisines.length > 0) {
      recommendationQuery['businessInfo.cuisine'] = { $in: preferredCuisines };
    }

    // Add location filter if provided
    if (userLocation && userLocation.coordinates) {
      recommendationQuery['location.coordinates'] = {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: userLocation.coordinates
          },
          $maxDistance: 10000 // 10km
        }
      };
    }

    const recommendations = await Restaurant.find(recommendationQuery)
      .sort({ 'ratings.average': -1, 'performance.totalOrders': -1 })
      .limit(10)
      .select('name businessInfo location ratings operationalInfo');

    return {
      preferredCuisines,
      recommendations: recommendations.map(restaurant => ({
        ...restaurant.toObject(),
        isCurrentlyOpen: isRestaurantOpen(restaurant.operationalInfo.businessHours),
        estimatedDeliveryTime: userLocation ? 
          calculateEstimatedDeliveryTime(
            restaurant.operationalInfo.avgPreparationTime,
            calculateDistance(restaurant.location.coordinates, userLocation.coordinates)
          ) : null
      }))
    };
  }
}

module.exports = new UserService();
