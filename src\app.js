const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');

const logger = require('./utils/logger');
const errorHandler = require('./middleware/errorHandler');
const notFoundHandler = require('./middleware/notFoundHandler');
const { specs, swaggerUi, swaggerOptions } = require('./config/swagger');

const authRoutes = require('./routes/auth');
const dashboardRoutes = require('./routes/dashboard');
const userRoutes = require('./routes/user');
const orderRoutes = require('./routes/orders');
const restaurantRoutes = require('./routes/restaurants');
const partnerRoutes = require('./routes/partner');
const restaurantPartnerRoutes = require('./routes/restaurant-partner');
const deliveryPartnerRoutes = require('./routes/delivery-partner');

const app = express();

const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests from this IP, please try again later.'
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(compression());

if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }));
}

app.use(limiter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

app.get('/health', async (req, res) => {
  try {
    const dbManager = require('./config/database');
    const dbStatus = dbManager.getConnectionStatus();

    res.json({
      success: true,
      message: 'Food Delivery API is running',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      version: process.env.API_VERSION,
      databases: dbStatus,
      features: {
        authentication: 'JWT + OTP',
        databases: 'MongoDB + PostgreSQL',
        apps: ['Dashboard', 'User App', 'Partner App']
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Health check failed',
      error: error.message
    });
  }
});

const apiVersion = process.env.API_VERSION || 'v1';
const baseRoute = `/api/${apiVersion}`;

// Swagger Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, swaggerOptions));

// API Documentation endpoint
app.get('/api-docs.json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(specs);
});

// API Routes
app.use(`${baseRoute}/auth`, authRoutes);
app.use(`${baseRoute}/dashboard`, dashboardRoutes);
app.use(`${baseRoute}/user`, userRoutes);
app.use(`${baseRoute}/orders`, orderRoutes);
app.use(`${baseRoute}/restaurants`, restaurantRoutes);
app.use(`${baseRoute}/partner`, partnerRoutes);
app.use(`${baseRoute}/restaurant-partner`, restaurantPartnerRoutes);
app.use(`${baseRoute}/delivery-partner`, deliveryPartnerRoutes);

app.use(notFoundHandler);
app.use(errorHandler);

module.exports = app;
