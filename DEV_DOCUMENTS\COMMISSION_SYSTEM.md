# 💰 Commission & Payment System (Zomato-Style)

## 📋 Overview

The Food Delivery Platform implements a sophisticated commission and payment system similar to Zomato, with separate models for **Restaurant Partners** and **Delivery Partners**.

## 🏪 Restaurant Partner Payment Model

### **Commission Structure**
```javascript
// Franchise-configurable commission rates
restaurantCommission: {
  defaultRate: 0.18,           // 18% base commission
  categoryRates: {
    "premium": 0.15,           // Premium restaurants (15%)
    "budget": 0.20,            // Budget restaurants (20%)
    "cloud_kitchen": 0.22,     // Cloud kitchens (22%)
    "cafe": 0.16,              // Cafes (16%)
    "bakery": 0.18,            // Bakeries (18%)
    "sweet_shop": 0.19         // Sweet shops (19%)
  },
  newPartnerRate: 0.12,        // First 3 months (12%)
  volumeDiscounts: [
    { threshold: 100, discount: 0.02 },  // 100+ orders/month: 2% discount
    { threshold: 500, discount: 0.05 }   // 500+ orders/month: 5% discount
  ]
}
```

### **Revenue Calculation**
```javascript
// Restaurant earnings per order
const calculateRestaurantEarnings = (order) => {
  const itemsTotal = order.pricing.itemsSubtotal;
  const taxes = order.pricing.taxes.total;
  const commission = itemsTotal * restaurant.commissionRate;
  const platformFee = order.pricing.platformFee;
  
  return {
    grossRevenue: itemsTotal + taxes,
    commission: commission,
    platformFee: platformFee,
    netEarnings: itemsTotal + taxes - commission - platformFee,
    tds: netEarnings * 0.01,  // 1% TDS
    finalSettlement: netEarnings - tds
  };
};
```

### **Settlement Cycles**
- **Daily**: High-volume restaurants (>50 orders/day)
- **Weekly**: Standard cycle (Monday settlements)
- **Monthly**: New restaurants (first 3 months)

## 🚚 Delivery Partner Payment Model

### **Base Earning Structure**
```javascript
deliveryEarnings: {
  baseDeliveryFee: 25,         // Base fee per delivery
  distanceRate: 5,             // ₹5 per km
  timeSlotMultipliers: {
    "peak_lunch": 1.5,         // 11 AM - 2 PM (50% extra)
    "peak_dinner": 1.8,        // 7 PM - 10 PM (80% extra)
    "late_night": 2.0,         // 10 PM - 2 AM (100% extra)
    "early_morning": 1.3,      // 6 AM - 9 AM (30% extra)
    "rain_bonus": 1.3,         // Rainy weather (30% extra)
    "festival_bonus": 1.5      // Festival days (50% extra)
  }
}
```

### **Dynamic Fee Calculation**
```javascript
const calculateDeliveryFee = (order, conditions) => {
  let baseFee = 25;
  let distanceFee = order.delivery.distance * 5;
  
  // Time-based multipliers
  const hour = new Date().getHours();
  let timeMultiplier = 1.0;
  
  if (hour >= 11 && hour <= 14) timeMultiplier = 1.5;      // Lunch peak
  else if (hour >= 19 && hour <= 22) timeMultiplier = 1.8; // Dinner peak
  else if (hour >= 22 || hour <= 2) timeMultiplier = 2.0;  // Late night
  
  // Weather & special conditions
  const weatherMultiplier = conditions.isRaining ? 1.3 : 1.0;
  const festivalMultiplier = conditions.isFestival ? 1.5 : 1.0;
  
  const totalFee = (baseFee + distanceFee) * timeMultiplier * weatherMultiplier * festivalMultiplier;
  
  return {
    baseFee,
    distanceFee,
    multipliers: { timeMultiplier, weatherMultiplier, festivalMultiplier },
    totalFee: Math.round(totalFee),
    partnerShare: Math.round(totalFee * 0.85)  // 85% to partner
  };
};
```

### **Incentive System**
```javascript
// Daily incentives
dailyIncentives: {
  completionBonus: {
    8: 100,    // 8 deliveries = ₹100 bonus
    12: 200,   // 12 deliveries = ₹200 bonus
    15: 350    // 15 deliveries = ₹350 bonus
  },
  peakHourBonus: {
    lunchPeak: 50,   // Working during lunch peak
    dinnerPeak: 75   // Working during dinner peak
  },
  ratingBonus: {
    minRating: 4.5,
    weeklyBonus: 50
  }
}

// Weekly incentives
weeklyIncentives: {
  consistencyBonus: {
    daysWorked: 6,     // Work 6 days
    bonus: 300         // Get ₹300 bonus
  },
  topPerformerBonus: {
    topPercentile: 10, // Top 10% performers
    bonus: 1000        // Get ₹1000 bonus
  },
  weekendBonus: 1.2    // 20% extra on weekends
}
```

## 🏢 Franchise-Level Configuration

### **Commission Management Dashboard**
Each franchise can configure:

```javascript
franchiseCommissionSettings: {
  // Restaurant commission rules
  restaurant: {
    defaultRate: 0.18,
    categorySpecificRates: true,
    volumeDiscountsEnabled: true,
    newPartnerIncentives: true,
    customContractualRates: true
  },
  
  // Delivery commission rules
  delivery: {
    baseCommission: 0.15,
    dynamicPricingEnabled: true,
    incentivePoolPercentage: 0.05,
    weatherBonusEnabled: true,
    peakHourMultipliers: true
  },
  
  // Platform fees
  platform: {
    serviceFee: 0.02,
    paymentGatewayFee: 0.025,
    gstRate: 0.18
  }
}
```

### **Real-time Rate Adjustments**
```javascript
// Automatic adjustments based on market conditions
dynamicAdjustments: {
  demandSupplyRatio: {
    highDemand: {
      deliveryMultiplier: 1.3,     // 30% extra for delivery partners
      restaurantDiscount: 0.02     // 2% commission discount for restaurants
    },
    lowDemand: {
      deliveryMultiplier: 0.9,     // 10% less for delivery partners
      restaurantSurcharge: 0.01    // 1% commission increase for restaurants
    }
  },
  
  seasonalAdjustments: {
    festivalSeason: {
      deliveryBonus: 1.5,
      restaurantDiscount: 0.03
    },
    monsoon: {
      deliveryBonus: 1.2,
      weatherAllowance: 20
    }
  }
}
```

## 📊 Settlement System

### **Restaurant Settlement (Weekly)**
```javascript
restaurantSettlement: {
  period: "weekly",              // Monday to Sunday
  cutoffTime: "23:59",          // Sunday 11:59 PM
  processingTime: "24h",        // Processed within 24 hours
  
  calculation: {
    grossRevenue: "sum(itemsSubtotal + taxes)",
    commission: "grossRevenue * commissionRate",
    platformFee: "sum(platformFee)",
    adjustments: "refunds + chargebacks + penalties",
    tds: "netAmount * 0.01",
    netSettlement: "grossRevenue - commission - platformFee - adjustments - tds"
  },
  
  minimumThreshold: 100,        // Minimum ₹100 for settlement
  holdPeriod: 7                 // 7 days hold for new restaurants
}
```

### **Delivery Partner Settlement (Daily)**
```javascript
deliverySettlement: {
  period: "daily",              // End of day settlement
  cutoffTime: "23:59",         // Daily cutoff
  processingTime: "2h",        // Processed within 2 hours
  
  calculation: {
    baseEarnings: "sum(deliveryFee * 0.85)",
    incentives: "dailyBonus + peakHourBonus + ratingBonus",
    penalties: "lateDelivery + customerComplaints",
    fuelAllowance: "fuelRate * deliveriesCompleted",
    netEarnings: "baseEarnings + incentives - penalties + fuelAllowance"
  },
  
  minimumThreshold: 50,         // Minimum ₹50 for settlement
  instantPayoutAvailable: true, // Option for instant payout (2% fee)
  instantPayoutFee: 0.02
}
```

## 💳 Payment Methods

### **Restaurant Partners**
- **Bank Transfer**: Standard weekly settlements
- **UPI**: For smaller amounts
- **Digital Wallets**: Paytm, PhonePe integration

### **Delivery Partners**
- **Daily Settlement**: Automatic bank transfer
- **Instant Payout**: Available 24/7 (2% fee)
- **UPI**: Real-time small payments
- **Cash Handling**: For COD orders

## 📈 Performance-Based Adjustments

### **Restaurant Performance Metrics**
```javascript
restaurantPerformance: {
  acceptanceRate: {
    excellent: ">95%",    // 1% commission discount
    good: "85-95%",       // No change
    poor: "<85%"          // 1% commission increase
  },
  
  preparationTime: {
    fast: "<20min",       // 0.5% commission discount
    average: "20-30min",  // No change
    slow: ">30min"        // 0.5% commission increase
  },
  
  customerRating: {
    excellent: ">4.5",    // 1% commission discount
    good: "4.0-4.5",      // No change
    poor: "<4.0"          // 1% commission increase
  }
}
```

### **Delivery Partner Performance Metrics**
```javascript
deliveryPerformance: {
  onTimeDelivery: {
    excellent: ">95%",    // 10% bonus on base fee
    good: "85-95%",       // 5% bonus on base fee
    poor: "<85%"          // No bonus
  },
  
  customerRating: {
    excellent: ">4.5",    // Weekly ₹100 bonus
    good: "4.0-4.5",      // Weekly ₹50 bonus
    poor: "<4.0"          // No bonus
  },
  
  completionRate: {
    excellent: ">98%",    // Priority order assignment
    good: "90-98%",       // Normal assignment
    poor: "<90%"          // Reduced assignments
  }
}
```

## 🔄 Real-time Updates

### **Commission Tracking**
- Real-time earnings calculation
- Live commission deduction display
- Instant settlement status updates
- Performance metric tracking

### **Transparency Features**
- Detailed commission breakdown
- Historical rate changes
- Performance impact on earnings
- Settlement timeline tracking

This commission system provides:
- ✅ **Zomato-style payment models** for both partner types
- ✅ **Franchise-level control** over commission rates
- ✅ **Dynamic pricing** based on demand and conditions
- ✅ **Performance-based adjustments**
- ✅ **Transparent settlement process**
- ✅ **Real-time earnings tracking**
- ✅ **Comprehensive incentive systems**
