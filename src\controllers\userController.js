const User = require('../models/User');
const Restaurant = require('../models/Restaurant');
const Order = require('../models/Order');
const logger = require('../utils/logger');
const { ERROR_CODES, SUCCESS_MESSAGES } = require('../utils/constants');
const { validationResult } = require('express-validator');

class UserController {
  async getProfile(req, res) {
    try {
      const user = await User.findById(req.user._id)
        .populate('franchiseId', 'name location.city');

      if (!user) {
        return res.status(404).json({
          success: false,
          error: {
            code: ERROR_CODES.NOT_FOUND,
            message: 'User not found'
          }
        });
      }

      // Get user statistics
      const [totalOrders, totalSpent, favoriteRestaurants] = await Promise.all([
        Order.countDocuments({ customerId: user._id }),
        Order.aggregate([
          {
            $match: {
              customerId: user._id,
              currentStatus: { $in: ['delivered', 'completed'] }
            }
          },
          {
            $group: {
              _id: null,
              totalSpent: { $sum: '$pricing.total' }
            }
          }
        ]),
        Order.aggregate([
          { $match: { customerId: user._id } },
          { $group: { _id: '$restaurantId', orderCount: { $sum: 1 } } },
          { $sort: { orderCount: -1 } },
          { $limit: 5 },
          {
            $lookup: {
              from: 'restaurants',
              localField: '_id',
              foreignField: '_id',
              as: 'restaurant'
            }
          },
          { $unwind: '$restaurant' },
          {
            $project: {
              _id: '$restaurant._id',
              name: '$restaurant.name',
              cuisine: '$restaurant.businessInfo.cuisine',
              orderCount: 1
            }
          }
        ])
      ]);

      res.json({
        success: true,
        message: 'Profile retrieved successfully',
        data: {
          user,
          stats: {
            totalOrders,
            totalSpent: totalSpent[0]?.totalSpent || 0,
            favoriteRestaurants
          }
        }
      });

    } catch (error) {
      logger.error('Get profile error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to retrieve profile'
        }
      });
    }
  }

  async updateProfile(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: {
            code: ERROR_CODES.VALIDATION_ERROR,
            message: 'Invalid input data',
            details: errors.array()
          }
        });
      }

      const { profile, settings } = req.body;
      const updateData = {};

      if (profile) {
        Object.keys(profile).forEach(key => {
          if (key !== 'addresses') { // Handle addresses separately
            updateData[`profile.${key}`] = profile[key];
          }
        });
      }

      if (settings) {
        Object.keys(settings).forEach(key => {
          updateData[`settings.${key}`] = settings[key];
        });
      }

      const user = await User.findByIdAndUpdate(
        req.user._id,
        { $set: updateData },
        { new: true, runValidators: true }
      );

      logger.info(`Profile updated for user: ${user.email}`);

      res.json({
        success: true,
        message: SUCCESS_MESSAGES.PROFILE_UPDATED,
        data: { user }
      });

    } catch (error) {
      logger.error('Update profile error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to update profile'
        }
      });
    }
  }

  async getRestaurants(req, res) {
    try {
      const { 
        page = 1, 
        limit = 10, 
        search, 
        cuisine, 
        sortBy = 'rating',
        latitude,
        longitude,
        maxDistance = 10000 // 10km in meters
      } = req.query;

      let query = { 
        status: 'active',
        verificationStatus: 'verified'
      };

      // Add search filter
      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { 'businessInfo.description': { $regex: search, $options: 'i' } },
          { 'businessInfo.cuisine': { $in: [new RegExp(search, 'i')] } }
        ];
      }

      // Add cuisine filter
      if (cuisine) {
        query['businessInfo.cuisine'] = { $in: cuisine.split(',') };
      }

      // Add location filter if coordinates provided
      if (latitude && longitude) {
        query['location.coordinates'] = {
          $near: {
            $geometry: {
              type: 'Point',
              coordinates: [parseFloat(longitude), parseFloat(latitude)]
            },
            $maxDistance: parseInt(maxDistance)
          }
        };
      }

      const skip = (parseInt(page) - 1) * parseInt(limit);

      // Build sort criteria
      let sortCriteria = {};
      switch (sortBy) {
        case 'rating':
          sortCriteria = { 'ratings.average': -1 };
          break;
        case 'deliveryTime':
          sortCriteria = { 'operationalInfo.avgPreparationTime': 1 };
          break;
        case 'distance':
          // Distance sorting is handled by $near in query
          break;
        case 'popularity':
          sortCriteria = { 'performance.totalOrders': -1 };
          break;
        default:
          sortCriteria = { 'ratings.average': -1 };
      }

      const [restaurants, total] = await Promise.all([
        Restaurant.find(query)
          .skip(skip)
          .limit(parseInt(limit))
          .sort(sortCriteria)
          .select('name businessInfo location operationalInfo ratings performance menu'),
        Restaurant.countDocuments(query)
      ]);

      // Calculate distance if coordinates provided
      const restaurantsWithDistance = restaurants.map(restaurant => {
        let distance = null;
        if (latitude && longitude) {
          const [restLng, restLat] = restaurant.location.coordinates;
          distance = calculateDistance(
            parseFloat(latitude), 
            parseFloat(longitude), 
            restLat, 
            restLng
          );
        }

        return {
          ...restaurant.toObject(),
          distance: distance ? Math.round(distance * 100) / 100 : null, // Round to 2 decimal places
          isCurrentlyOpen: isRestaurantOpen(restaurant.operationalInfo.businessHours),
          estimatedDeliveryTime: restaurant.operationalInfo.avgPreparationTime + (distance ? Math.ceil(distance * 2) : 30)
        };
      });

      res.json({
        success: true,
        message: 'Restaurants retrieved successfully',
        data: restaurantsWithDistance,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        },
        filters: {
          search,
          cuisine,
          sortBy,
          location: latitude && longitude ? { latitude, longitude, maxDistance } : null
        }
      });

    } catch (error) {
      logger.error('Get restaurants error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to retrieve restaurants'
        }
      });
    }
  }

  async getOrders(req, res) {
    try {
      const { page = 1, limit = 10, status } = req.query;

      let query = { customerId: req.user._id };

      if (status) {
        query.currentStatus = status;
      }

      const skip = (parseInt(page) - 1) * parseInt(limit);

      const [orders, total] = await Promise.all([
        Order.find(query)
          .populate('restaurantId', 'name businessInfo.cuisine location.address')
          .populate('deliveryPartnerId', 'profile.name phone')
          .skip(skip)
          .limit(parseInt(limit))
          .sort({ createdAt: -1 }),
        Order.countDocuments(query)
      ]);

      res.json({
        success: true,
        message: 'Orders retrieved successfully',
        data: orders,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });

    } catch (error) {
      logger.error('Get orders error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to retrieve orders'
        }
      });
    }
  }

  async getAddresses(req, res) {
    try {
      const user = await User.findById(req.user._id).select('profile.addresses');

      res.json({
        success: true,
        message: 'Addresses retrieved successfully',
        data: user.profile.addresses || []
      });

    } catch (error) {
      logger.error('Get addresses error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to retrieve addresses'
        }
      });
    }
  }

  async addAddress(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: {
            code: ERROR_CODES.VALIDATION_ERROR,
            message: 'Invalid input data',
            details: errors.array()
          }
        });
      }

      const { type, address, coordinates, landmark, isDefault } = req.body;

      const user = await User.findById(req.user._id);

      // If this is set as default, unset other default addresses
      if (isDefault) {
        user.profile.addresses.forEach(addr => {
          addr.isDefault = false;
        });
      }

      // Add new address
      user.profile.addresses.push({
        type,
        address,
        coordinates,
        landmark,
        isDefault: isDefault || user.profile.addresses.length === 0 // First address is default
      });

      await user.save();

      logger.info(`Address added for user: ${user.email}`);

      res.status(201).json({
        success: true,
        message: 'Address added successfully',
        data: {
          address: user.profile.addresses[user.profile.addresses.length - 1]
        }
      });

    } catch (error) {
      logger.error('Add address error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to add address'
        }
      });
    }
  }
}

// Helper functions
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c; // Distance in kilometers
}

function isRestaurantOpen(businessHours) {
  const now = new Date();
  const currentDay = now.toLocaleDateString('en-US', { weekday: 'lowercase' });
  const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

  const todayHours = businessHours[currentDay];
  if (!todayHours || !todayHours.isOpen) {
    return false;
  }

  return currentTime >= todayHours.open && currentTime <= todayHours.close;
}

module.exports = new UserController();
