# 🎛️ DASHBOARD DEVELOPMENT GUIDE
## Super Admin & Franchise Admin Dashboards

### 📋 **PROJECT OVERVIEW**

This guide provides comprehensive documentation for developing **Super Admin** and **Franchise Admin** dashboards using the existing Food Delivery Platform API. The API is fully functional and production-ready with complete authentication, role-based access control, and comprehensive business logic.

---

## 🏗️ **ARCHITECTURE OVERVIEW**

### **Current API Status: ✅ 100% COMPLETE**
- **Base URL**: `http://localhost:3000/api/v1`
- **Authentication**: JWT-based with role-based access control
- **Database**: Hybrid MongoDB + PostgreSQL architecture
- **Documentation**: Swagger UI available at `/api-docs`

### **Dashboard Types**
1. **Super Admin Dashboard**: Full platform management
2. **Franchise Admin Dashboard**: Franchise-specific management

---

## 🔐 **AUTHENTICATION SYSTEM**

### **Available User Roles**
```javascript
const USER_ROLES = {
  SUPER_ADMIN: 'super_admin',
  FRANCHISE_ADMIN: 'franchise_admin',
  RESTAURANT_OWNER: 'restaurant_owner',
  DELIVERY_PARTNER: 'delivery_partner',
  CUSTOMER: 'customer'
};
```

### **Authentication Flow**
1. **Registration**: `POST /api/v1/auth/register`
2. **Login**: `POST /api/v1/auth/login`
3. **OTP Verification**: `POST /api/v1/auth/verify-otp` (Fixed OTP: 123456)
4. **Token Refresh**: `POST /api/v1/auth/refresh-token`

### **Sample Admin Registration**
```json
{
  "email": "<EMAIL>",
  "phone": "+919876543210",
  "password": "SecurePass123",
  "role": "super_admin",
  "profile": {
    "name": "Super Admin",
    "adminInfo": {
      "department": "Operations",
      "permissions": ["all"]
    }
  }
}
```

---

## 📊 **DASHBOARD API ENDPOINTS**

### **Base Route**: `/api/v1/dashboard/`
**Authentication Required**: JWT Bearer Token
**Authorized Roles**: Super Admin, Franchise Admin

| Method | Endpoint | Description | Super Admin | Franchise Admin |
|--------|----------|-------------|-------------|-----------------|
| `GET` | `/stats` | Platform statistics | ✅ All data | ✅ Franchise-specific |
| `GET` | `/franchises` | Franchise management | ✅ All franchises | ✅ Own franchise only |
| `GET` | `/users` | User management | ✅ All users | ✅ Franchise users only |
| `GET` | `/orders` | Order management | ✅ All orders | ✅ Franchise orders only |

---

## 📈 **DASHBOARD STATISTICS API**

### **Endpoint**: `GET /api/v1/dashboard/stats`

### **Response Structure**
```json
{
  "success": true,
  "message": "Dashboard statistics retrieved successfully",
  "data": {
    "overview": {
      "totalUsers": 1250,
      "totalRestaurants": 85,
      "totalOrders": 3420,
      "activeRestaurants": 72,
      "todayOrders": 156,
      "monthlyRevenue": 125000
    },
    "orderStats": {
      "statusDistribution": {
        "pending": 23,
        "confirmed": 45,
        "preparing": 32,
        "ready": 18,
        "picked_up": 12,
        "delivered": 2890,
        "cancelled": 67
      },
      "todayOrders": 156
    },
    "topRestaurants": [
      {
        "_id": "restaurant_id",
        "name": "Pizza Palace",
        "totalOrders": 245,
        "totalRevenue": 18500,
        "ratings": { "average": 4.5, "count": 189 },
        "cuisine": ["Italian", "Fast Food"]
      }
    ],
    "period": {
      "from": "2024-01-01T00:00:00.000Z",
      "to": "2024-01-31T23:59:59.999Z"
    }
  }
}
```

### **Key Metrics Available**
- **Overview Metrics**: Users, restaurants, orders, revenue
- **Order Analytics**: Status distribution, daily trends
- **Performance Data**: Top restaurants, revenue analysis
- **Real-time Data**: Active restaurants, today's orders

---

## 🏢 **FRANCHISE MANAGEMENT API**

### **Endpoint**: `GET /api/v1/dashboard/franchises`

### **Query Parameters**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search by name, owner, or city
- `status`: Filter by franchise status

### **Response Structure**
```json
{
  "success": true,
  "data": {
    "franchises": [
      {
        "_id": "franchise_id",
        "name": "Mumbai Central Franchise",
        "owner": {
          "name": "John Doe",
          "email": "<EMAIL>",
          "phone": "+919876543210"
        },
        "location": {
          "address": "Mumbai, Maharashtra",
          "city": "Mumbai",
          "coordinates": [72.8777, 19.0760]
        },
        "serviceArea": {
          "radius": 15,
          "zones": ["Bandra", "Andheri", "Juhu"]
        },
        "commission": {
          "restaurant": 15,
          "delivery": 8,
          "platform": 5
        },
        "status": "active",
        "stats": {
          "totalRestaurants": 25,
          "totalOrders": 1250,
          "monthlyRevenue": 85000
        }
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 45,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

---

## 👥 **USER MANAGEMENT API**

### **Endpoint**: `GET /api/v1/dashboard/users`

### **Query Parameters**
- `page`, `limit`: Pagination
- `search`: Search by email, name, or phone
- `role`: Filter by user role
- `status`: Filter by user status

### **Response Structure**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "_id": "user_id",
        "email": "<EMAIL>",
        "phone": "+919876543210",
        "role": "customer",
        "status": "active",
        "profile": {
          "name": "Customer Name",
          "addresses": [
            {
              "type": "home",
              "address": "123 Main St, Mumbai",
              "coordinates": [72.8777, 19.0760]
            }
          ]
        },
        "createdAt": "2024-01-15T10:30:00.000Z",
        "lastLogin": "2024-01-30T14:20:00.000Z",
        "stats": {
          "totalOrders": 45,
          "totalSpent": 12500,
          "averageOrderValue": 278
        }
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 125,
      "totalItems": 1250
    }
  }
}
```

---

## 📦 **ORDER MANAGEMENT API**

### **Endpoint**: `GET /api/v1/dashboard/orders`

### **Query Parameters**
- `page`, `limit`: Pagination
- `status`: Filter by order status
- `dateFrom`, `dateTo`: Date range filter
- `restaurantId`: Filter by restaurant

### **Response Structure**
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "_id": "order_id",
        "orderNumber": "ORD-2024-001234",
        "customerId": "customer_id",
        "restaurantId": "restaurant_id",
        "currentStatus": "delivered",
        "items": [
          {
            "name": "Margherita Pizza",
            "quantity": 2,
            "price": 299,
            "total": 598
          }
        ],
        "pricing": {
          "subtotal": 598,
          "taxes": 47.84,
          "deliveryFee": 40,
          "total": 685.84
        },
        "delivery": {
          "address": "123 Main St, Mumbai",
          "partnerId": "delivery_partner_id",
          "estimatedTime": 30,
          "actualTime": 28
        },
        "timestamps": {
          "placed": "2024-01-30T12:00:00.000Z",
          "confirmed": "2024-01-30T12:02:00.000Z",
          "delivered": "2024-01-30T12:28:00.000Z"
        }
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 342,
      "totalItems": 3420
    }
  }
}
```

---

## 🎨 **DASHBOARD UI COMPONENTS**

### **Recommended Tech Stack**
- **Frontend**: React.js with TypeScript
- **UI Library**: Material-UI or Ant Design
- **Charts**: Chart.js or Recharts
- **State Management**: Redux Toolkit or Zustand
- **HTTP Client**: Axios
- **Routing**: React Router

### **Core Components Structure**
```
src/
├── components/
│   ├── Dashboard/
│   │   ├── StatsCards.jsx
│   │   ├── OrderChart.jsx
│   │   ├── RevenueChart.jsx
│   │   └── TopRestaurants.jsx
│   ├── Franchise/
│   │   ├── FranchiseList.jsx
│   │   ├── FranchiseCard.jsx
│   │   └── FranchiseModal.jsx
│   ├── Users/
│   │   ├── UserList.jsx
│   │   ├── UserCard.jsx
│   │   └── UserFilters.jsx
│   └── Orders/
│       ├── OrderList.jsx
│       ├── OrderCard.jsx
│       └── OrderFilters.jsx
├── pages/
│   ├── Dashboard.jsx
│   ├── Franchises.jsx
│   ├── Users.jsx
│   └── Orders.jsx
├── services/
│   ├── api.js
│   ├── auth.js
│   └── dashboard.js
└── utils/
    ├── constants.js
    └── helpers.js
```

---

## 🔧 **IMPLEMENTATION EXAMPLES**

### **Authentication Service**
```javascript
// services/auth.js
import axios from 'axios';

const API_BASE = 'http://localhost:3000/api/v1';

class AuthService {
  async login(credentials) {
    const response = await axios.post(`${API_BASE}/auth/login`, credentials);
    if (response.data.success) {
      localStorage.setItem('token', response.data.data.accessToken);
      localStorage.setItem('refreshToken', response.data.data.refreshToken);
    }
    return response.data;
  }

  async verifyOTP(phone, otp) {
    const response = await axios.post(`${API_BASE}/auth/verify-otp`, {
      phone,
      otp
    });
    return response.data;
  }

  getToken() {
    return localStorage.getItem('token');
  }

  isAuthenticated() {
    return !!this.getToken();
  }

  logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
  }
}

export default new AuthService();
```

### **Dashboard API Service**
```javascript
// services/dashboard.js
import axios from 'axios';

const API_BASE = 'http://localhost:3000/api/v1/dashboard';

class DashboardService {
  constructor() {
    this.api = axios.create({
      baseURL: API_BASE,
    });

    // Add auth interceptor
    this.api.interceptors.request.use((config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });
  }

  async getStats() {
    const response = await this.api.get('/stats');
    return response.data;
  }

  async getFranchises(params = {}) {
    const response = await this.api.get('/franchises', { params });
    return response.data;
  }

  async getUsers(params = {}) {
    const response = await this.api.get('/users', { params });
    return response.data;
  }

  async getOrders(params = {}) {
    const response = await this.api.get('/orders', { params });
    return response.data;
  }
}

export default new DashboardService();
```

### **Dashboard Stats Component**
```jsx
// components/Dashboard/StatsCards.jsx
import React from 'react';
import { Card, Row, Col, Statistic } from 'antd';
import {
  UserOutlined,
  ShopOutlined,
  ShoppingCartOutlined,
  DollarOutlined
} from '@ant-design/icons';

const StatsCards = ({ stats }) => {
  const { overview } = stats;

  return (
    <Row gutter={16}>
      <Col span={6}>
        <Card>
          <Statistic
            title="Total Users"
            value={overview.totalUsers}
            prefix={<UserOutlined />}
            valueStyle={{ color: '#3f8600' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="Total Restaurants"
            value={overview.totalRestaurants}
            prefix={<ShopOutlined />}
            valueStyle={{ color: '#cf1322' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="Total Orders"
            value={overview.totalOrders}
            prefix={<ShoppingCartOutlined />}
            valueStyle={{ color: '#1890ff' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="Monthly Revenue"
            value={overview.monthlyRevenue}
            prefix={<DollarOutlined />}
            precision={2}
            valueStyle={{ color: '#722ed1' }}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default StatsCards;
```

### **Order Status Chart Component**
```jsx
// components/Dashboard/OrderChart.jsx
import React from 'react';
import { Card } from 'antd';
import { Pie } from 'react-chartjs-2';

const OrderChart = ({ orderStats }) => {
  const { statusDistribution } = orderStats;

  const chartData = {
    labels: Object.keys(statusDistribution),
    datasets: [
      {
        data: Object.values(statusDistribution),
        backgroundColor: [
          '#FF6384',
          '#36A2EB',
          '#FFCE56',
          '#4BC0C0',
          '#9966FF',
          '#FF9F40',
          '#FF6384'
        ],
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'bottom',
      },
      title: {
        display: true,
        text: 'Order Status Distribution',
      },
    },
  };

  return (
    <Card title="Order Analytics" style={{ height: 400 }}>
      <Pie data={chartData} options={options} />
    </Card>
  );
};

export default OrderChart;
```

---

## 🎯 **DASHBOARD PAGES IMPLEMENTATION**

### **Main Dashboard Page**
```jsx
// pages/Dashboard.jsx
import React, { useState, useEffect } from 'react';
import { Layout, Row, Col, Spin, message } from 'antd';
import StatsCards from '../components/Dashboard/StatsCards';
import OrderChart from '../components/Dashboard/OrderChart';
import TopRestaurants from '../components/Dashboard/TopRestaurants';
import DashboardService from '../services/dashboard';

const { Content } = Layout;

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState(null);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      const response = await DashboardService.getStats();
      if (response.success) {
        setStats(response.data);
      } else {
        message.error('Failed to fetch dashboard statistics');
      }
    } catch (error) {
      message.error('Error loading dashboard data');
      console.error('Dashboard error:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <Content style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <StatsCards stats={stats} />
      </div>

      <Row gutter={16}>
        <Col span={12}>
          <OrderChart orderStats={stats.orderStats} />
        </Col>
        <Col span={12}>
          <TopRestaurants restaurants={stats.topRestaurants} />
        </Col>
      </Row>
    </Content>
  );
};

export default Dashboard;
```

### **Franchise Management Page**
```jsx
// pages/Franchises.jsx
import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Input,
  Select,
  Button,
  Space,
  Tag,
  message
} from 'antd';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import DashboardService from '../services/dashboard';

const { Search } = Input;
const { Option } = Select;

const Franchises = () => {
  const [loading, setLoading] = useState(false);
  const [franchises, setFranchises] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [filters, setFilters] = useState({
    search: '',
    status: '',
  });

  useEffect(() => {
    fetchFranchises();
  }, [pagination.current, pagination.pageSize, filters]);

  const fetchFranchises = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        ...filters,
      };

      const response = await DashboardService.getFranchises(params);
      if (response.success) {
        setFranchises(response.data.franchises);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination.totalItems,
        }));
      }
    } catch (error) {
      message.error('Failed to fetch franchises');
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: 'Franchise Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            {record.location.city}
          </div>
        </div>
      ),
    },
    {
      title: 'Owner',
      dataIndex: ['owner', 'name'],
      key: 'owner',
      render: (text, record) => (
        <div>
          <div>{text}</div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            {record.owner.email}
          </div>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Restaurants',
      dataIndex: ['stats', 'totalRestaurants'],
      key: 'restaurants',
    },
    {
      title: 'Orders',
      dataIndex: ['stats', 'totalOrders'],
      key: 'orders',
    },
    {
      title: 'Revenue',
      dataIndex: ['stats', 'monthlyRevenue'],
      key: 'revenue',
      render: (value) => `₹${value?.toLocaleString() || 0}`,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button type="link" size="small">
            View Details
          </Button>
          <Button type="link" size="small">
            Edit
          </Button>
        </Space>
      ),
    },
  ];

  const handleTableChange = (paginationConfig) => {
    setPagination(paginationConfig);
  };

  const handleSearch = (value) => {
    setFilters(prev => ({ ...prev, search: value }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  const handleStatusFilter = (value) => {
    setFilters(prev => ({ ...prev, status: value }));
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  return (
    <Card
      title="Franchise Management"
      extra={
        <Button type="primary" icon={<PlusOutlined />}>
          Add Franchise
        </Button>
      }
    >
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Search
            placeholder="Search franchises..."
            allowClear
            onSearch={handleSearch}
            style={{ width: 300 }}
          />
          <Select
            placeholder="Filter by status"
            allowClear
            style={{ width: 150 }}
            onChange={handleStatusFilter}
          >
            <Option value="active">Active</Option>
            <Option value="inactive">Inactive</Option>
            <Option value="pending">Pending</Option>
          </Select>
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={franchises}
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        rowKey="_id"
      />
    </Card>
  );
};

export default Franchises;
```

---

## 🔒 **ROLE-BASED ACCESS CONTROL**

### **Permission Hook**
```jsx
// hooks/usePermissions.js
import { useContext } from 'react';
import { AuthContext } from '../contexts/AuthContext';

const usePermissions = () => {
  const { user } = useContext(AuthContext);

  const isSuperAdmin = () => user?.role === 'super_admin';
  const isFranchiseAdmin = () => user?.role === 'franchise_admin';
  const isAdmin = () => isSuperAdmin() || isFranchiseAdmin();

  const canViewAllFranchises = () => isSuperAdmin();
  const canViewAllUsers = () => isSuperAdmin();
  const canViewAllOrders = () => isSuperAdmin();

  const canManageFranchise = (franchiseId) => {
    if (isSuperAdmin()) return true;
    if (isFranchiseAdmin()) return user.franchiseId === franchiseId;
    return false;
  };

  return {
    isSuperAdmin,
    isFranchiseAdmin,
    isAdmin,
    canViewAllFranchises,
    canViewAllUsers,
    canViewAllOrders,
    canManageFranchise,
  };
};

export default usePermissions;
```

### **Protected Route Component**
```jsx
// components/ProtectedRoute.jsx
import React from 'react';
import { Navigate } from 'react-router-dom';
import usePermissions from '../hooks/usePermissions';

const ProtectedRoute = ({ children, requiredRole, requiredPermission }) => {
  const permissions = usePermissions();

  // Check role-based access
  if (requiredRole === 'super_admin' && !permissions.isSuperAdmin()) {
    return <Navigate to="/unauthorized" replace />;
  }

  if (requiredRole === 'admin' && !permissions.isAdmin()) {
    return <Navigate to="/unauthorized" replace />;
  }

  // Check specific permissions
  if (requiredPermission && !permissions[requiredPermission]()) {
    return <Navigate to="/unauthorized" replace />;
  }

  return children;
};

export default ProtectedRoute;
```

---

## 🚀 **DEPLOYMENT SETUP**

### **Environment Configuration**
```javascript
// config/environment.js
const config = {
  development: {
    API_BASE_URL: 'http://localhost:3000/api/v1',
    SOCKET_URL: 'http://localhost:3000',
  },
  production: {
    API_BASE_URL: 'https://your-api-domain.com/api/v1',
    SOCKET_URL: 'https://your-api-domain.com',
  },
};

export default config[process.env.NODE_ENV || 'development'];
```

### **Build Scripts**
```json
{
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "build:prod": "NODE_ENV=production npm run build",
    "serve": "serve -s build -l 3001"
  }
}
```

---

## 📋 **DEVELOPMENT CHECKLIST**

### **Phase 1: Setup & Authentication** ✅
- [x] API is fully functional and tested
- [ ] Setup React dashboard project
- [ ] Implement authentication flow
- [ ] Create login/OTP verification pages
- [ ] Setup routing and protected routes

### **Phase 2: Core Dashboard**
- [ ] Implement dashboard statistics page
- [ ] Create stats cards and charts
- [ ] Add real-time data updates
- [ ] Implement responsive design

### **Phase 3: Management Pages**
- [ ] Build franchise management page
- [ ] Create user management interface
- [ ] Implement order management system
- [ ] Add search and filtering capabilities

### **Phase 4: Advanced Features**
- [ ] Add real-time notifications
- [ ] Implement data export functionality
- [ ] Create detailed analytics views
- [ ] Add bulk operations

### **Phase 5: Testing & Deployment**
- [ ] Unit testing for components
- [ ] Integration testing with API
- [ ] Performance optimization
- [ ] Production deployment

---

## 🎯 **NEXT STEPS**

1. **Start Development**: Create React dashboard project
2. **Test API**: Verify all endpoints work correctly
3. **Implement Authentication**: Build login and OTP flows
4. **Create Dashboard**: Build main statistics dashboard
5. **Add Management**: Implement franchise, user, and order management

**Your API is ready! Time to build the dashboard! 🚀**
```
