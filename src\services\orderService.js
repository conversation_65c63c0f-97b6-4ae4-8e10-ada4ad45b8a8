const Order = require('../models/Order');
const Restaurant = require('../models/Restaurant');
const logger = require('../utils/logger');
const { ERROR_CODES, ORDER_STATUS } = require('../utils/constants');
const pricingService = require('./pricingService');
const validationService = require('./validationService');
const realtimeEventService = require('./realtimeEventService');
const { calculateDistance, generateOrderNumber } = require('../utils/helpers');

class OrderService {
  async validateOrderRequest(restaurantId, items, deliveryAddress) {
    // Validate restaurant
    const restaurant = await Restaurant.findById(restaurantId);
    if (!restaurant || restaurant.status !== 'active') {
      throw new Error('Restaurant not available');
    }

    // Check if restaurant is currently open
    if (!restaurant.operationalInfo.acceptingOrders) {
      throw new Error('Restaurant is not accepting orders currently');
    }

    // Validate menu items
    const validatedItems = await this.validateMenuItems(restaurant, items);
    
    return { restaurant, validatedItems };
  }

  async validateMenuItems(restaurant, items) {
    let itemsSubtotal = 0;
    const validatedItems = [];

    for (const item of items) {
      const menuItem = restaurant.menu.id(item.menuItemId);
      if (!menuItem || !menuItem.isAvailable) {
        throw new Error(`Menu item ${item.menuItemId} is not available`);
      }

      let itemPrice = menuItem.price;
      let itemTotal = itemPrice * item.quantity;

      // Add variant pricing
      if (item.variant && menuItem.variants) {
        const variant = menuItem.variants.find(v => v.name === item.variant);
        if (variant) {
          itemPrice = variant.price;
          itemTotal = itemPrice * item.quantity;
        }
      }

      // Add addon pricing
      let addonTotal = 0;
      if (item.addons && item.addons.length > 0) {
        for (const addon of item.addons) {
          const menuAddon = menuItem.addons.find(a => a.name === addon.name);
          if (menuAddon && menuAddon.isAvailable) {
            addonTotal += menuAddon.price * addon.quantity;
          }
        }
      }

      const finalItemTotal = (itemTotal + addonTotal);
      itemsSubtotal += finalItemTotal;

      validatedItems.push({
        menuItemId: item.menuItemId,
        name: menuItem.name,
        price: itemPrice,
        quantity: item.quantity,
        variant: item.variant,
        addons: item.addons || [],
        specialInstructions: item.specialInstructions,
        itemTotal: finalItemTotal
      });
    }

    return { validatedItems, itemsSubtotal };
  }

  async createOrder(orderData) {
    const {
      restaurant,
      validatedItems,
      itemsSubtotal,
      customerId,
      deliveryAddress,
      paymentMethod,
      couponCode,
      specialInstructions
    } = orderData;

    // Check minimum order amount
    if (itemsSubtotal < restaurant.operationalInfo.minOrderAmount) {
      throw new Error(`Minimum order amount is ₹${restaurant.operationalInfo.minOrderAmount}`);
    }

    // Calculate pricing
    const pricing = await pricingService.calculateOrderPricing({
      itemsSubtotal,
      restaurant,
      deliveryAddress,
      couponCode
    });

    // Generate order number
    const orderNumber = generateOrderNumber();

    // Create order object
    const order = new Order({
      orderNumber,
      franchiseId: restaurant.franchiseId,
      customerId,
      restaurantId: restaurant._id,
      items: validatedItems.validatedItems,
      pricing,
      addresses: {
        pickup: {
          restaurantName: restaurant.name,
          address: restaurant.location.address,
          coordinates: restaurant.location.coordinates,
          contactNumber: restaurant.contactInfo?.phone || ''
        },
        delivery: deliveryAddress
      },
      timeline: [{
        status: ORDER_STATUS.PLACED,
        timestamp: new Date(),
        note: 'Order placed by customer'
      }],
      currentStatus: ORDER_STATUS.PLACED,
      delivery: {
        estimatedPickupTime: new Date(Date.now() + restaurant.operationalInfo.avgPreparationTime * 60000),
        estimatedDeliveryTime: new Date(Date.now() + (restaurant.operationalInfo.avgPreparationTime + 30) * 60000),
        deliveryDistance: calculateDistance(restaurant.location.coordinates, deliveryAddress.coordinates)
      },
      payment: {
        method: paymentMethod,
        status: paymentMethod === 'cod' ? 'pending' : 'pending',
        paidAmount: paymentMethod === 'cod' ? 0 : pricing.total
      },
      specialInstructions
    });

    await order.save();

    // Update restaurant performance
    await Restaurant.findByIdAndUpdate(restaurant._id, {
      $inc: { 'performance.totalOrders': 1 }
    });

    logger.info(`Order placed: ${orderNumber} by customer: ${customerId}`);

    // Emit real-time event for order creation
    realtimeEventService.emitOrderCreated(order);

    return order;
  }

  async getOrderById(orderId, userId, userRole) {
    const order = await Order.findById(orderId)
      .populate('restaurantId', 'name businessInfo.cuisine location.address contactInfo')
      .populate('deliveryPartnerId', 'profile.name phone profile.deliveryPartnerInfo.currentLocation');

    if (!order) {
      throw new Error('Order not found');
    }

    // Check access permissions
    if (!this.hasOrderAccess(order, userId, userRole)) {
      throw new Error('Access denied');
    }

    return order;
  }

  hasOrderAccess(order, userId, userRole) {
    return (
      order.customerId.toString() === userId.toString() ||
      userRole === 'super_admin' ||
      userRole === 'franchise_admin' ||
      (userRole === 'restaurant_owner' && order.restaurantId.ownerId?.toString() === userId.toString()) ||
      (userRole === 'delivery_partner' && order.deliveryPartnerId?.toString() === userId.toString())
    );
  }

  async updateOrderStatus(orderId, newStatus, user, note, estimatedTime) {
    const order = await Order.findById(orderId);
    if (!order) {
      throw new Error('Order not found');
    }

    // Validate status transition and user permissions
    const validation = validationService.validateStatusUpdate(order, newStatus, user);
    if (!validation.allowed) {
      throw new Error(validation.message);
    }

    // Update order status
    order.currentStatus = newStatus;
    order.timeline.push({
      status: newStatus,
      timestamp: new Date(),
      note: note || `Status updated to ${newStatus}`,
      updatedBy: user._id
    });

    // Update specific fields based on status
    this.updateOrderByStatus(order, newStatus, user, estimatedTime);

    await order.save();

    logger.info(`Order ${order.orderNumber} status updated to ${newStatus} by ${user.email}`);

    // Emit real-time event for status update
    realtimeEventService.emitOrderStatusUpdated(order);

    return order;
  }

  updateOrderByStatus(order, status, user, estimatedTime) {
    switch (status) {
      case ORDER_STATUS.ACCEPTED:
        if (estimatedTime) {
          order.delivery.estimatedPickupTime = new Date(Date.now() + estimatedTime * 60000);
          order.delivery.estimatedDeliveryTime = new Date(Date.now() + (estimatedTime + 30) * 60000);
        }
        break;
      
      case ORDER_STATUS.PICKED_UP:
        order.delivery.actualPickupTime = new Date();
        order.deliveryPartnerId = user._id;
        break;
      
      case ORDER_STATUS.DELIVERED:
        order.delivery.actualDeliveryTime = new Date();
        order.payment.status = 'completed';
        break;
    }
  }

  async cancelOrder(orderId, userId, reason) {
    const order = await Order.findById(orderId);
    if (!order) {
      throw new Error('Order not found');
    }

    // Check if user can cancel this order
    if (order.customerId.toString() !== userId.toString()) {
      throw new Error('You can only cancel your own orders');
    }

    // Check if order can be cancelled
    const cancellableStatuses = [ORDER_STATUS.PLACED, ORDER_STATUS.ACCEPTED, ORDER_STATUS.PREPARING];
    if (!cancellableStatuses.includes(order.currentStatus)) {
      throw new Error('Order cannot be cancelled at this stage');
    }

    // Cancel order
    order.currentStatus = ORDER_STATUS.CANCELLED;
    order.timeline.push({
      status: ORDER_STATUS.CANCELLED,
      timestamp: new Date(),
      note: reason || 'Order cancelled by customer',
      cancelledBy: userId
    });

    // Update payment status
    if (order.payment.method === 'online' && order.payment.status === 'completed') {
      order.payment.status = 'refund_pending';
      order.payment.refundAmount = order.pricing.total;
    }

    await order.save();

    logger.info(`Order ${order.orderNumber} cancelled by user: ${userId}`);

    // Emit real-time event for order cancellation
    realtimeEventService.emitOrderCancelled(order);

    return order;
  }

  async getOrdersByUser(userId, filters = {}) {
    const { page = 1, limit = 10, status } = filters;
    let query = { customerId: userId };

    if (status) {
      query.currentStatus = status;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [orders, total] = await Promise.all([
      Order.find(query)
        .populate('restaurantId', 'name businessInfo.cuisine location.address')
        .populate('deliveryPartnerId', 'profile.name phone')
        .skip(skip)
        .limit(parseInt(limit))
        .sort({ createdAt: -1 }),
      Order.countDocuments(query)
    ]);

    return {
      orders,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    };
  }
}

module.exports = new OrderService();
