# 🤝 Partner System - Restaurant & Delivery Partners

## 📋 Overview

The Food Delivery Platform supports **two distinct partner types** with separate apps and payment models similar to Zomato:

1. **Restaurant Partners** - Manage restaurants, menus, orders, and earnings
2. **Delivery Partners** - Handle order pickup, delivery, and earnings

## 🏪 Restaurant Partner System

### **Restaurant Partner App Features**
- **Order Management**: Accept/reject orders, update preparation status
- **Menu Management**: Add/edit items, categories, pricing, availability
- **Sales Analytics**: Daily/weekly/monthly sales reports
- **Payment Dashboard**: Earnings, commission breakdown, settlement details
- **Restaurant Profile**: Business hours, delivery zones, contact info
- **Performance Metrics**: Ratings, delivery time, acceptance rate

### **Restaurant Payment Model (Zomato-style)**

#### **Commission Structure**
```javascript
// Per franchise configurable rates
restaurantCommission: {
  baseRate: 0.18,           // 18% base commission
  volumeDiscounts: [{
    minOrders: 100,         // Monthly orders
    discountRate: 0.02      // 2% discount (16% total)
  }, {
    minOrders: 500,
    discountRate: 0.05      // 5% discount (13% total)
  }],
  categoryRates: {
    "premium": 0.15,        // Premium restaurants
    "cloud_kitchen": 0.20,  // Cloud kitchens
    "cafe": 0.16           // Cafes
  }
}
```

#### **Settlement Calculation**
```javascript
// Restaurant earnings calculation
const calculateRestaurantEarnings = (order) => {
  const itemsTotal = order.pricing.itemsSubtotal;
  const taxes = order.pricing.taxes.total;
  const commission = itemsTotal * restaurant.commissionRate;
  const platformFee = order.pricing.platformFee;
  
  return {
    grossRevenue: itemsTotal + taxes,
    commission: commission,
    platformFee: platformFee,
    netEarnings: itemsTotal + taxes - commission - platformFee,
    tds: calculateTDS(netEarnings), // Tax deduction at source
    finalSettlement: netEarnings - tds
  };
};
```

#### **Payment Cycles**
- **Daily**: For high-volume restaurants (>50 orders/day)
- **Weekly**: Standard cycle (Monday settlements)
- **Monthly**: For new restaurants (first 3 months)

## 🚚 Delivery Partner System

### **Delivery Partner App Features**
- **Order Queue**: Available orders for pickup
- **Navigation**: GPS-based route optimization
- **Earnings Tracker**: Real-time earnings, incentives, bonuses
- **Performance Dashboard**: Ratings, delivery time, completion rate
- **Availability Control**: Online/offline status, working hours
- **Support**: In-app chat, emergency contacts

### **Delivery Payment Model (Zomato-style)**

#### **Base Earning Structure**
```javascript
deliveryEarnings: {
  baseDeliveryFee: 25,      // Base fee per delivery
  distanceRate: 5,          // Per km rate
  timeSlotMultipliers: {
    "peak_lunch": 1.5,      // 11 AM - 2 PM
    "peak_dinner": 1.8,     // 7 PM - 10 PM
    "late_night": 2.0,      // 10 PM - 2 AM
    "rain_bonus": 1.3       // Weather-based bonus
  },
  incentives: {
    dailyTarget: {
      orders: 8,
      bonus: 100
    },
    weeklyTarget: {
      orders: 50,
      bonus: 500
    },
    ratingBonus: {
      minRating: 4.5,
      bonus: 50              // Weekly bonus
    }
  }
}
```

#### **Dynamic Pricing Factors**
```javascript
const calculateDeliveryFee = (order, partner, conditions) => {
  let baseFee = franchise.deliverySettings.base;
  let distanceFee = order.delivery.distance * franchise.deliverySettings.perKm;
  
  // Time-based multipliers
  const timeMultiplier = getTimeMultiplier(order.createdAt);
  
  // Weather conditions
  const weatherMultiplier = conditions.isRaining ? 1.3 : 1.0;
  
  // Demand-supply ratio
  const demandMultiplier = calculateDemandSupply(order.location);
  
  const totalFee = (baseFee + distanceFee) * timeMultiplier * weatherMultiplier * demandMultiplier;
  
  return {
    baseFee,
    distanceFee,
    multipliers: { timeMultiplier, weatherMultiplier, demandMultiplier },
    totalFee: Math.round(totalFee)
  };
};
```

#### **Incentive System**
```javascript
// Daily incentives
dailyIncentives: {
  completionBonus: {
    8: 100,    // 8 orders = ₹100 bonus
    12: 200,   // 12 orders = ₹200 bonus
    15: 350    // 15 orders = ₹350 bonus
  },
  peakHourBonus: {
    lunchPeak: 50,   // Working during lunch peak
    dinnerPeak: 75   // Working during dinner peak
  },
  weekendBonus: 1.2  // 20% extra on weekends
}

// Weekly incentives
weeklyIncentives: {
  consistencyBonus: {
    daysWorked: 6,
    bonus: 300
  },
  topPerformerBonus: {
    topPercentile: 10,  // Top 10% performers
    bonus: 1000
  }
}
```

## 🏢 Franchise-Level Configuration

### **Commission Management**
Each franchise can configure:

```javascript
franchiseCommissionSettings: {
  restaurant: {
    defaultRate: 0.18,
    categoryRates: {
      "premium": 0.15,
      "budget": 0.20,
      "cloud_kitchen": 0.22
    },
    volumeDiscounts: [{
      threshold: 100,    // Monthly orders
      discount: 0.02     // 2% reduction
    }],
    newPartnerRate: 0.12,  // First 3 months
    contractualRates: {}   // Custom rates for specific restaurants
  },
  
  delivery: {
    baseCommission: 0.15,  // 15% of delivery fee
    partnerShare: 0.85,    // 85% goes to delivery partner
    fuelSurcharge: 5,      // Additional ₹5 during fuel price hikes
    incentivePool: 0.05    // 5% for incentive programs
  },
  
  platform: {
    serviceFee: 0.02,      // 2% platform fee
    paymentGateway: 0.025, // 2.5% payment processing
    gst: 0.18             // 18% GST on commission
  }
}
```

### **Dynamic Rate Adjustment**
```javascript
// Automatic rate adjustments based on market conditions
dynamicRateAdjustment: {
  demandSupplyRatio: {
    highDemand: {          // More orders than partners
      deliveryMultiplier: 1.3,
      restaurantDiscount: 0.02
    },
    lowDemand: {           // More partners than orders
      deliveryMultiplier: 0.9,
      restaurantSurcharge: 0.01
    }
  },
  
  seasonalAdjustments: {
    festivalSeason: {
      deliveryBonus: 1.5,
      restaurantDiscount: 0.03
    },
    monsoon: {
      deliveryBonus: 1.2,
      weatherAllowance: 20
    }
  }
}
```

## 📊 Payment Settlement System

### **Restaurant Settlement**
```javascript
// Weekly settlement for restaurants
restaurantSettlement: {
  period: "weekly",        // Monday to Sunday
  cutoffTime: "23:59",     // Sunday 11:59 PM
  processingTime: "24h",   // Processed within 24 hours
  
  calculation: {
    grossRevenue: "sum(order.itemsSubtotal + order.taxes)",
    commission: "grossRevenue * commissionRate",
    platformFee: "sum(order.platformFee)",
    adjustments: "refunds + chargebacks + penalties",
    tds: "netAmount * 0.01", // 1% TDS for businesses
    netSettlement: "grossRevenue - commission - platformFee - adjustments - tds"
  },
  
  minimumThreshold: 100,   // Minimum ₹100 for settlement
  holdPeriod: 7           // 7 days hold for new restaurants
}
```

### **Delivery Partner Settlement**
```javascript
// Daily settlement for delivery partners
deliverySettlement: {
  period: "daily",         // End of day settlement
  cutoffTime: "23:59",     // Daily cutoff
  processingTime: "2h",    // Processed within 2 hours
  
  calculation: {
    baseEarnings: "sum(deliveryFee * partnerShare)",
    incentives: "dailyBonus + peakHourBonus + ratingBonus",
    penalties: "lateDelivery + customerComplaints",
    fuel: "fuelAllowance * deliveriesCompleted",
    netEarnings: "baseEarnings + incentives - penalties + fuel"
  },
  
  minimumThreshold: 50,    // Minimum ₹50 for settlement
  instantPayout: true      // Option for instant payout (2% fee)
}
```

## 🔄 Real-time Updates

### **Order Status Flow**
```javascript
// Restaurant Partner App
orderStatusFlow: {
  "placed": {
    action: "accept/reject",
    timeLimit: 120,        // 2 minutes to respond
    autoReject: true
  },
  "accepted": {
    action: "start_preparation",
    estimatedTime: "required"
  },
  "preparing": {
    action: "ready_for_pickup",
    updates: "preparation_progress"
  },
  "ready": {
    action: "notify_delivery_partner",
    waitTime: "max_15_minutes"
  }
}

// Delivery Partner App
deliveryStatusFlow: {
  "ready": {
    action: "accept_pickup",
    location: "required"
  },
  "picked_up": {
    action: "start_delivery",
    otp: "required_from_restaurant"
  },
  "out_for_delivery": {
    action: "complete_delivery",
    otp: "required_from_customer"
  },
  "delivered": {
    action: "collect_payment", // For COD orders
    rating: "optional"
  }
}
```

This enhanced partner system provides:
- ✅ **Separate apps** for restaurant and delivery partners
- ✅ **Zomato-style payment models** with dynamic pricing
- ✅ **Franchise-level commission control**
- ✅ **Comprehensive incentive systems**
- ✅ **Real-time earnings tracking**
- ✅ **Performance-based bonuses**
