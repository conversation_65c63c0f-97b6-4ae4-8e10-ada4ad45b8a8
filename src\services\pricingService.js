const { calculateDistance } = require('../utils/helpers');

class PricingService {
  constructor() {
    this.TAX_RATE = 0.10; // 10% total tax (5% CGST + 5% SGST)
    this.PLATFORM_FEE_RATE = 0.02; // 2% platform fee
    this.BASE_DELIVERY_FEE = 25; // Base delivery fee in rupees
    this.DELIVERY_FEE_PER_KM = 5; // Additional fee per kilometer
  }

  async calculateOrderPricing({ itemsSubtotal, restaurant, deliveryAddress, couponCode }) {
    // Calculate taxes
    const taxAmount = this.calculateTax(itemsSubtotal);

    // Calculate delivery fee
    const deliveryFee = this.calculateDeliveryFee(restaurant, deliveryAddress);

    // Calculate platform fee
    const platformFee = this.calculatePlatformFee(itemsSubtotal);

    // Apply coupon discount
    const discountInfo = this.applyCouponDiscount(itemsSubtotal, couponCode);

    // Calculate total
    const total = itemsSubtotal + taxAmount + deliveryFee + platformFee - discountInfo.discountAmount;

    return {
      itemsSubtotal,
      taxes: {
        cgst: taxAmount / 2,
        sgst: taxAmount / 2,
        total: taxAmount
      },
      deliveryFee,
      platformFee,
      discounts: discountInfo.couponCode ? {
        couponCode: discountInfo.couponCode,
        discountAmount: discountInfo.discountAmount,
        description: discountInfo.description
      } : null,
      total: Math.round(total * 100) / 100 // Round to 2 decimal places
    };
  }

  calculateTax(subtotal) {
    return Math.round(subtotal * this.TAX_RATE * 100) / 100;
  }

  calculateDeliveryFee(restaurant, deliveryAddress) {
    const distance = calculateDistance(
      restaurant.location.coordinates,
      deliveryAddress.coordinates
    );
    
    const distanceFee = Math.ceil(distance) * this.DELIVERY_FEE_PER_KM;
    return this.BASE_DELIVERY_FEE + distanceFee;
  }

  calculatePlatformFee(subtotal) {
    return Math.round(subtotal * this.PLATFORM_FEE_RATE);
  }

  applyCouponDiscount(subtotal, couponCode) {
    if (!couponCode) {
      return { discountAmount: 0 };
    }

    // Simplified coupon logic - in production, this would query a coupons database
    const coupons = {
      'FIRST20': {
        type: 'percentage',
        value: 0.20,
        maxDiscount: 100,
        description: '20% off up to ₹100'
      },
      'FLAT50': {
        type: 'flat',
        value: 50,
        minOrder: 200,
        description: '₹50 off on orders above ₹200'
      },
      'WELCOME10': {
        type: 'percentage',
        value: 0.10,
        maxDiscount: 50,
        description: '10% off up to ₹50'
      }
    };

    const coupon = coupons[couponCode.toUpperCase()];
    if (!coupon) {
      return { discountAmount: 0 };
    }

    let discountAmount = 0;

    if (coupon.type === 'percentage') {
      discountAmount = subtotal * coupon.value;
      if (coupon.maxDiscount) {
        discountAmount = Math.min(discountAmount, coupon.maxDiscount);
      }
    } else if (coupon.type === 'flat') {
      if (!coupon.minOrder || subtotal >= coupon.minOrder) {
        discountAmount = coupon.value;
      }
    }

    return {
      couponCode: couponCode.toUpperCase(),
      discountAmount: Math.round(discountAmount * 100) / 100,
      description: coupon.description
    };
  }

  calculateCommission(orderTotal, restaurantCommissionRate = 0.15) {
    // Calculate platform commission from restaurant
    const platformCommission = orderTotal * restaurantCommissionRate;
    
    return {
      platformCommission: Math.round(platformCommission * 100) / 100,
      restaurantEarnings: Math.round((orderTotal - platformCommission) * 100) / 100,
      commissionRate: restaurantCommissionRate
    };
  }

  calculateDeliveryPartnerEarnings(deliveryFee, distance, basePay = 20) {
    // Base pay + distance-based pay + delivery fee share
    const distancePay = distance * 3; // ₹3 per km
    const deliveryFeeShare = deliveryFee * 0.8; // 80% of delivery fee
    
    const totalEarnings = basePay + distancePay + deliveryFeeShare;
    
    return {
      basePay,
      distancePay: Math.round(distancePay * 100) / 100,
      deliveryFeeShare: Math.round(deliveryFeeShare * 100) / 100,
      totalEarnings: Math.round(totalEarnings * 100) / 100
    };
  }

  validateCoupon(couponCode, subtotal, userId) {
    // This would typically check database for coupon validity, usage limits, etc.
    // For now, returning simplified validation
    
    const coupon = this.applyCouponDiscount(subtotal, couponCode);
    
    if (coupon.discountAmount === 0) {
      return {
        valid: false,
        message: 'Invalid or expired coupon code'
      };
    }

    return {
      valid: true,
      discount: coupon,
      message: `Coupon applied successfully! You save ₹${coupon.discountAmount}`
    };
  }

  calculateRefundAmount(order, cancellationReason = 'customer_request') {
    const { pricing, payment, currentStatus } = order;
    
    // Refund policies based on order status
    const refundPolicies = {
      'placed': 1.0, // 100% refund
      'accepted': 1.0, // 100% refund
      'preparing': 0.8, // 80% refund (20% cancellation fee)
      'ready': 0.5, // 50% refund
      'picked_up': 0.0, // No refund
      'out_for_delivery': 0.0, // No refund
      'delivered': 0.0 // No refund
    };

    const refundPercentage = refundPolicies[currentStatus] || 0;
    
    if (payment.method === 'cod') {
      return {
        refundAmount: 0,
        refundPercentage,
        message: 'No refund required for cash on delivery orders'
      };
    }

    const refundAmount = pricing.total * refundPercentage;
    const cancellationFee = pricing.total - refundAmount;

    return {
      refundAmount: Math.round(refundAmount * 100) / 100,
      cancellationFee: Math.round(cancellationFee * 100) / 100,
      refundPercentage,
      estimatedRefundTime: '3-5 business days',
      message: refundPercentage === 1.0 ? 
        'Full refund will be processed' : 
        `Partial refund after ${Math.round((1 - refundPercentage) * 100)}% cancellation fee`
    };
  }

  generatePricingBreakdown(pricing) {
    return {
      itemsSubtotal: `₹${pricing.itemsSubtotal}`,
      taxes: `₹${pricing.taxes.total} (CGST: ₹${pricing.taxes.cgst}, SGST: ₹${pricing.taxes.sgst})`,
      deliveryFee: `₹${pricing.deliveryFee}`,
      platformFee: `₹${pricing.platformFee}`,
      discount: pricing.discounts ? `-₹${pricing.discounts.discountAmount} (${pricing.discounts.description})` : '₹0',
      total: `₹${pricing.total}`,
      savings: pricing.discounts ? `₹${pricing.discounts.discountAmount}` : '₹0'
    };
  }
}

module.exports = new PricingService();
