require('dotenv').config();
require('express-async-errors');

const express = require('express');
const http = require('http');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');

// Import services
const socketService = require('./src/services/socketService');
const realtimeEventService = require('./src/services/realtimeEventService');
const logger = require('./src/utils/logger');
const { specs, swaggerUi, swaggerOptions } = require('./src/config/swagger');

// Import routes
const authRoutes = require('./src/routes/auth');
const dashboardRoutes = require('./src/routes/dashboard');
const userRoutes = require('./src/routes/user');
const orderRoutes = require('./src/routes/orders');
const restaurantRoutes = require('./src/routes/restaurants');
const partnerRoutes = require('./src/routes/partner');
const restaurantPartnerRoutes = require('./src/routes/restaurant-partner');
const deliveryPartnerRoutes = require('./src/routes/delivery-partner');

// Create Express app
const app = express();
const server = http.createServer(app);

// Initialize Socket.io
socketService.initialize(server);

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests from this IP, please try again later.'
    }
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Security middleware
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS configuration
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(compression());
app.use(morgan('combined'));
app.use(limiter);
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint with real-time status
app.get('/health', (req, res) => {
  const connectedUsers = socketService.getConnectedUsers();
  const deliveryPartners = socketService.getDeliveryPartners();
  
  res.json({
    success: true,
    message: 'Food Delivery API with Real-time Functionality',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    version: process.env.API_VERSION || 'v1',
    realtime: {
      status: 'active',
      connectedUsers: connectedUsers.length,
      activeDeliveryPartners: deliveryPartners.filter(p => p.isAvailable).length,
      totalDeliveryPartners: deliveryPartners.length,
      socketConnections: socketService.io ? socketService.io.engine.clientsCount : 0
    },
    documentation: {
      swagger: 'http://localhost:3000/api-docs',
      json: 'http://localhost:3000/api-docs.json'
    },
    features: {
      authentication: 'JWT + OTP',
      realtime: 'Socket.io + Event-driven architecture',
      databases: 'MongoDB + PostgreSQL',
      apps: ['Dashboard', 'User App', 'Partner App'],
      documentation: 'Swagger UI with interactive testing'
    }
  });
});

// Real-time status endpoint
app.get('/realtime/status', (req, res) => {
  const connectedUsers = socketService.getConnectedUsers();
  const deliveryPartners = socketService.getDeliveryPartners();
  
  res.json({
    success: true,
    message: 'Real-time service status',
    data: {
      socketServer: {
        status: socketService.io ? 'running' : 'stopped',
        connections: socketService.io ? socketService.io.engine.clientsCount : 0,
        rooms: socketService.io ? Object.keys(socketService.io.sockets.adapter.rooms) : []
      },
      connectedUsers: {
        total: connectedUsers.length,
        users: connectedUsers
      },
      deliveryPartners: {
        total: deliveryPartners.length,
        active: deliveryPartners.filter(p => p.isAvailable).length,
        partners: deliveryPartners.map(p => ({
          partnerId: p.partnerId,
          isAvailable: p.isAvailable,
          hasLocation: !!(p.latitude && p.longitude),
          lastUpdate: p.timestamp
        }))
      },
      eventService: {
        status: 'running',
        listeners: realtimeEventService.eventNames().length
      }
    }
  });
});

// Test real-time functionality
app.post('/realtime/test', (req, res) => {
  const { event, data } = req.body;
  
  try {
    switch (event) {
      case 'order-created':
        realtimeEventService.emitOrderCreated(data);
        break;
      case 'order-status-updated':
        realtimeEventService.emitOrderStatusUpdated(data);
        break;
      case 'delivery-location-updated':
        realtimeEventService.emitDeliveryLocationUpdated(data);
        break;
      default:
        return res.status(400).json({
          success: false,
          error: { message: 'Unknown event type' }
        });
    }
    
    res.json({
      success: true,
      message: `Test event '${event}' emitted successfully`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: { message: error.message }
    });
  }
});

// Swagger Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, swaggerOptions));

// API Documentation JSON endpoint
app.get('/api-docs.json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(specs);
});

// API Routes
const apiVersion = process.env.API_VERSION || 'v1';
const baseRoute = `/api/${apiVersion}`;

app.use(`${baseRoute}/auth`, authRoutes);
app.use(`${baseRoute}/dashboard`, dashboardRoutes);
app.use(`${baseRoute}/user`, userRoutes);
app.use(`${baseRoute}/orders`, orderRoutes);
app.use(`${baseRoute}/restaurants`, restaurantRoutes);
app.use(`${baseRoute}/partner`, partnerRoutes);
app.use(`${baseRoute}/restaurant-partner`, restaurantPartnerRoutes);
app.use(`${baseRoute}/delivery-partner`, deliveryPartnerRoutes);

// Mock real-time endpoints for testing
app.get(`${baseRoute}/mock/order-status-update`, (req, res) => {
  const mockOrder = {
    _id: '507f1f77bcf86cd799439011',
    orderNumber: 'ORD-20241201-1234',
    customerId: '507f1f77bcf86cd799439012',
    restaurantId: '507f1f77bcf86cd799439013',
    currentStatus: 'preparing',
    pricing: { total: 535 }
  };
  
  realtimeEventService.emitOrderStatusUpdated(mockOrder);
  
  res.json({
    success: true,
    message: 'Mock order status update sent',
    data: mockOrder
  });
});

app.get(`${baseRoute}/mock/delivery-location`, (req, res) => {
  const mockLocationData = {
    orderId: '507f1f77bcf86cd799439011',
    deliveryPartnerId: '507f1f77bcf86cd799439014',
    location: {
      latitude: 19.0760 + (Math.random() - 0.5) * 0.01,
      longitude: 72.8777 + (Math.random() - 0.5) * 0.01
    }
  };
  
  realtimeEventService.emitDeliveryLocationUpdated(mockLocationData);
  
  res.json({
    success: true,
    message: 'Mock delivery location update sent',
    data: mockLocationData
  });
});

// API endpoints summary
app.get(`${baseRoute}/endpoints`, (req, res) => {
  res.json({
    success: true,
    message: 'Food Delivery Platform API with Real-time Features',
    documentation: {
      interactive: `http://localhost:3000/api-docs`,
      json: `http://localhost:3000/api-docs.json`,
      description: 'Complete API documentation with interactive testing'
    },
    realtime: {
      status: `http://localhost:3000/realtime/status`,
      test: `http://localhost:3000/realtime/test`,
      mockEndpoints: [
        `GET ${baseRoute}/mock/order-status-update`,
        `GET ${baseRoute}/mock/delivery-location`
      ]
    },
    endpoints: {
      authentication: [
        'POST /auth/register - User registration',
        'POST /auth/login - User login',
        'POST /auth/verify-otp - OTP verification (use 123456)',
        'POST /auth/refresh-token - Token refresh'
      ],
      user: [
        'GET /user/restaurants - Restaurant discovery with geospatial search',
        'GET /user/profile - User profile',
        'GET /user/orders - Order history'
      ],
      orders: [
        'POST /orders - Place new order (triggers real-time events)',
        'GET /orders/:orderId - Get order details',
        'PUT /orders/:orderId/status - Update order status (triggers real-time events)',
        'DELETE /orders/:orderId - Cancel order (triggers real-time events)'
      ],
      realtime: [
        'Socket.io connection on same port',
        'Real-time order status updates',
        'Live delivery tracking',
        'Restaurant notifications',
        'Admin dashboard updates'
      ]
    },
    socketEvents: {
      client: [
        'connect - Establish connection with JWT token',
        'location-update - Update delivery partner location',
        'availability-status - Update delivery partner availability',
        'restaurant-status - Update restaurant open/close status'
      ],
      server: [
        'connected - Connection confirmation',
        'order-status-update - Order status changes',
        'delivery-location-update - Live delivery tracking',
        'new-order - New order notifications',
        'notification - General notifications'
      ]
    },
    testInstructions: {
      step1: 'Visit http://localhost:3000/api-docs for API documentation',
      step2: 'Connect to Socket.io with JWT token for real-time features',
      step3: 'Use mock endpoints to test real-time functionality',
      step4: 'Monitor /realtime/status for connection status',
      step5: 'Test order placement and status updates for real-time events'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    documentation: 'Visit http://localhost:3000/api-docs for complete API documentation',
    realtime: 'Visit http://localhost:3000/realtime/status for real-time service status'
  });
});

// Error handler
app.use((error, req, res, next) => {
  logger.error('Server error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: error.message
  });
});

const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
  console.log('🎉 ===============================================');
  console.log('🚀 FOOD DELIVERY API WITH REAL-TIME FEATURES');
  console.log('🎉 ===============================================');
  console.log(`✅ Server running on: http://localhost:${PORT}`);
  console.log(`📚 Swagger Documentation: http://localhost:${PORT}/api-docs`);
  console.log(`🔄 Real-time Status: http://localhost:${PORT}/realtime/status`);
  console.log(`📋 API Endpoints: http://localhost:${PORT}${baseRoute}/endpoints`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
  console.log('');
  console.log('🔄 Real-time Features:');
  console.log('   • Socket.io server for WebSocket connections');
  console.log('   • Live order status updates');
  console.log('   • Real-time delivery tracking');
  console.log('   • Restaurant notifications');
  console.log('   • Admin dashboard live updates');
  console.log('');
  console.log('🧪 Test Real-time:');
  console.log(`   • GET ${baseRoute}/mock/order-status-update`);
  console.log(`   • GET ${baseRoute}/mock/delivery-location`);
  console.log('   • Connect to Socket.io with JWT token');
  console.log('');
  console.log('🎯 Ready for real-time food delivery operations!');
  console.log('🎉 ===============================================');
});
