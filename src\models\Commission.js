const mongoose = require('mongoose');

const commissionRuleSchema = new mongoose.Schema({
  franchiseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Franchise',
    required: true
  },
  
  // Restaurant Commission Rules
  restaurant: {
    defaultRate: {
      type: Number,
      required: true,
      min: 0,
      max: 1,
      default: 0.18
    },
    categoryRates: {
      premium: { type: Number, min: 0, max: 1, default: 0.15 },
      budget: { type: Number, min: 0, max: 1, default: 0.20 },
      cloud_kitchen: { type: Number, min: 0, max: 1, default: 0.22 },
      cafe: { type: Number, min: 0, max: 1, default: 0.16 },
      bakery: { type: Number, min: 0, max: 1, default: 0.18 },
      sweet_shop: { type: Number, min: 0, max: 1, default: 0.19 }
    },
    volumeDiscounts: [{
      threshold: { type: Number, required: true }, // Monthly orders
      discount: { type: Number, required: true }   // Percentage discount
    }],
    newPartnerRate: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.12
    },
    newPartnerDuration: {
      type: Number,
      default: 90 // Days
    },
    contractualRates: [{
      restaurantId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Restaurant'
      },
      rate: { type: Number, min: 0, max: 1 },
      validFrom: Date,
      validTo: Date
    }]
  },
  
  // Delivery Commission Rules
  delivery: {
    baseCommission: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.15
    },
    partnerShare: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.85
    },
    baseDeliveryFee: {
      type: Number,
      default: 25
    },
    distanceRate: {
      type: Number,
      default: 5
    },
    timeSlotMultipliers: {
      peak_lunch: { type: Number, default: 1.5 },    // 11 AM - 2 PM
      peak_dinner: { type: Number, default: 1.8 },   // 7 PM - 10 PM
      late_night: { type: Number, default: 2.0 },    // 10 PM - 2 AM
      early_morning: { type: Number, default: 1.3 }, // 6 AM - 9 AM
      rain_bonus: { type: Number, default: 1.3 },
      festival_bonus: { type: Number, default: 1.5 }
    },
    incentives: {
      dailyTarget: {
        orders: { type: Number, default: 8 },
        bonus: { type: Number, default: 100 }
      },
      weeklyTarget: {
        orders: { type: Number, default: 50 },
        bonus: { type: Number, default: 500 }
      },
      ratingBonus: {
        minRating: { type: Number, default: 4.5 },
        bonus: { type: Number, default: 50 }
      },
      completionBonus: {
        8: { type: Number, default: 100 },
        12: { type: Number, default: 200 },
        15: { type: Number, default: 350 }
      },
      peakHourBonus: {
        lunchPeak: { type: Number, default: 50 },
        dinnerPeak: { type: Number, default: 75 }
      },
      weekendBonus: { type: Number, default: 1.2 }
    },
    fuelSurcharge: {
      type: Number,
      default: 5
    },
    incentivePool: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.05
    }
  },
  
  // Platform Fees
  platform: {
    serviceFee: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.02
    },
    paymentGateway: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.025
    },
    gst: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.18
    }
  },
  
  // Dynamic Adjustments
  dynamicRules: {
    demandSupplyAdjustment: {
      enabled: { type: Boolean, default: true },
      highDemandMultiplier: { type: Number, default: 1.3 },
      lowDemandMultiplier: { type: Number, default: 0.9 },
      restaurantHighDemandDiscount: { type: Number, default: 0.02 },
      restaurantLowDemandSurcharge: { type: Number, default: 0.01 }
    },
    seasonalAdjustments: {
      enabled: { type: Boolean, default: true },
      festivalSeason: {
        deliveryBonus: { type: Number, default: 1.5 },
        restaurantDiscount: { type: Number, default: 0.03 }
      },
      monsoon: {
        deliveryBonus: { type: Number, default: 1.2 },
        weatherAllowance: { type: Number, default: 20 }
      }
    }
  },
  
  // Settlement Configuration
  settlement: {
    restaurant: {
      cycle: {
        type: String,
        enum: ['daily', 'weekly', 'monthly'],
        default: 'weekly'
      },
      cutoffTime: {
        type: String,
        default: '23:59'
      },
      processingTime: {
        type: String,
        default: '24h'
      },
      minimumThreshold: {
        type: Number,
        default: 100
      },
      holdPeriod: {
        type: Number,
        default: 7 // Days
      },
      tdsRate: {
        type: Number,
        default: 0.01 // 1%
      }
    },
    delivery: {
      cycle: {
        type: String,
        enum: ['daily', 'weekly'],
        default: 'daily'
      },
      cutoffTime: {
        type: String,
        default: '23:59'
      },
      processingTime: {
        type: String,
        default: '2h'
      },
      minimumThreshold: {
        type: Number,
        default: 50
      },
      instantPayoutFee: {
        type: Number,
        default: 0.02 // 2%
      }
    }
  },
  
  effectiveFrom: {
    type: Date,
    default: Date.now
  },
  effectiveTo: {
    type: Date,
    default: null
  },
  isActive: {
    type: Boolean,
    default: true
  },
  
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Indexes
commissionRuleSchema.index({ franchiseId: 1, isActive: 1 });
commissionRuleSchema.index({ effectiveFrom: 1, effectiveTo: 1 });

// Methods
commissionRuleSchema.methods.calculateRestaurantCommission = function(order, restaurant) {
  let rate = this.restaurant.defaultRate;
  
  // Check category rate
  if (restaurant.businessInfo.type && this.restaurant.categoryRates[restaurant.businessInfo.type]) {
    rate = this.restaurant.categoryRates[restaurant.businessInfo.type];
  }
  
  // Check contractual rate
  const contractualRate = this.restaurant.contractualRates.find(
    cr => cr.restaurantId.toString() === restaurant._id.toString() &&
          new Date() >= cr.validFrom && new Date() <= cr.validTo
  );
  if (contractualRate) {
    rate = contractualRate.rate;
  }
  
  // Check new partner rate
  const daysSinceOnboarding = (Date.now() - restaurant.onboardingDate) / (1000 * 60 * 60 * 24);
  if (daysSinceOnboarding <= this.restaurant.newPartnerDuration) {
    rate = this.restaurant.newPartnerRate;
  }
  
  // Apply volume discounts (would need monthly order count)
  // This would be calculated based on historical data
  
  return order.pricing.itemsSubtotal * rate;
};

commissionRuleSchema.methods.calculateDeliveryFee = function(order, conditions = {}) {
  let baseFee = this.delivery.baseDeliveryFee;
  let distanceFee = order.delivery.deliveryDistance * this.delivery.distanceRate;
  
  // Time-based multipliers
  const hour = new Date(order.createdAt).getHours();
  let timeMultiplier = 1.0;
  
  if (hour >= 11 && hour <= 14) timeMultiplier = this.delivery.timeSlotMultipliers.peak_lunch;
  else if (hour >= 19 && hour <= 22) timeMultiplier = this.delivery.timeSlotMultipliers.peak_dinner;
  else if (hour >= 22 || hour <= 2) timeMultiplier = this.delivery.timeSlotMultipliers.late_night;
  else if (hour >= 6 && hour <= 9) timeMultiplier = this.delivery.timeSlotMultipliers.early_morning;
  
  // Weather conditions
  const weatherMultiplier = conditions.isRaining ? this.delivery.timeSlotMultipliers.rain_bonus : 1.0;
  
  // Festival bonus
  const festivalMultiplier = conditions.isFestival ? this.delivery.timeSlotMultipliers.festival_bonus : 1.0;
  
  const totalFee = (baseFee + distanceFee) * timeMultiplier * weatherMultiplier * festivalMultiplier;
  
  return {
    baseFee,
    distanceFee,
    multipliers: { timeMultiplier, weatherMultiplier, festivalMultiplier },
    totalFee: Math.round(totalFee),
    partnerShare: Math.round(totalFee * this.delivery.partnerShare)
  };
};

module.exports = mongoose.model('CommissionRule', commissionRuleSchema);
