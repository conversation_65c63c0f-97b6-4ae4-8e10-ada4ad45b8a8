# 🗄️ DATABASE SETUP GUIDE

## 📋 **OVERVIEW**

Your Food Delivery Platform API uses a **hybrid database approach**:
- **MongoDB**: Primary database for operational data (users, restaurants, orders)
- **PostgreSQL**: Financial database for transactions and settlements

---

## 🚀 **QUICK SETUP OPTIONS**

### **Option 1: Docker Setup (Recommended)**
```bash
# Start both databases with Docker
docker-compose up -d

# Check if containers are running
docker ps
```

### **Option 2: Local Installation**
Follow the detailed steps below for each database.

---

## 🍃 **MONGODB SETUP**

### **Windows Installation:**
1. **Download MongoDB Community Server**
   - Visit: https://www.mongodb.com/try/download/community
   - Download Windows MSI installer
   - Run installer with default settings

2. **Start MongoDB Service**
   ```cmd
   # Start MongoDB service
   net start MongoDB
   
   # Or start manually
   "C:\Program Files\MongoDB\Server\7.0\bin\mongod.exe" --dbpath "C:\data\db"
   ```

3. **Install MongoDB Shell (mongosh)**
   ```cmd
   # Download from: https://www.mongodb.com/try/download/shell
   # Or install via npm
   npm install -g mongosh
   ```

4. **Test Connection**
   ```bash
   mongosh
   # Should connect to mongodb://localhost:27017
   ```

### **Alternative: MongoDB Atlas (Cloud)**
1. Create free account at https://www.mongodb.com/atlas
2. Create cluster and get connection string
3. Update `.env` file:
   ```env
   MONGODB_URI=mongodb+srv://username:<EMAIL>/food_delivery_platform
   ```

---

## 🐘 **POSTGRESQL SETUP**

### **Windows Installation:**
1. **Download PostgreSQL**
   - Visit: https://www.postgresql.org/download/windows/
   - Download and run installer
   - Remember the password you set for 'postgres' user

2. **Start PostgreSQL Service**
   ```cmd
   # PostgreSQL should start automatically
   # Check if running:
   services.msc
   # Look for "postgresql-x64-15" service
   ```

3. **Test Connection**
   ```cmd
   # Connect using psql
   psql -U postgres -h localhost
   # Enter password when prompted
   ```

4. **Create Database and User**
   ```sql
   -- Connect as postgres user
   CREATE USER myuser WITH PASSWORD 'mypassword';
   CREATE DATABASE mydatabase OWNER myuser;
   GRANT ALL PRIVILEGES ON DATABASE mydatabase TO myuser;
   \q
   ```

### **Alternative: PostgreSQL Cloud**
- **Heroku Postgres**: Free tier available
- **AWS RDS**: PostgreSQL managed service
- **Google Cloud SQL**: PostgreSQL managed service

---

## 🔧 **ENVIRONMENT CONFIGURATION**

### **Update .env File**
```env
# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/food_delivery_platform
MONGODB_TEST_URI=mongodb://localhost:27017/food_delivery_platform_test

# PostgreSQL Configuration
POSTGRESQL_URI=postgresql://myuser:mypassword@localhost:5432/mydatabase
POSTGRESQL_HOST=localhost
POSTGRESQL_PORT=5432
POSTGRESQL_USER=myuser
POSTGRESQL_PASSWORD=mypassword
POSTGRESQL_DB=mydatabase
```

---

## 🐳 **DOCKER SETUP (EASIEST)**

### **Create docker-compose.yml**
```yaml
version: '3.8'
services:
  mongodb:
    image: mongo:7.0
    container_name: food_delivery_mongo
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: food_delivery_platform
    volumes:
      - mongodb_data:/data/db

  postgresql:
    image: postgres:15
    container_name: food_delivery_postgres
    restart: always
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: myuser
      POSTGRES_PASSWORD: mypassword
      POSTGRES_DB: mydatabase
    volumes:
      - postgresql_data:/var/lib/postgresql/data

volumes:
  mongodb_data:
  postgresql_data:
```

### **Start Databases**
```bash
# Start databases
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs mongodb
docker-compose logs postgresql

# Stop databases
docker-compose down
```

---

## ✅ **VERIFY SETUP**

### **Test MongoDB Connection**
```bash
# Using mongosh
mongosh mongodb://localhost:27017/food_delivery_platform

# Test commands
use food_delivery_platform
db.test.insertOne({message: "Hello MongoDB"})
db.test.find()
```

### **Test PostgreSQL Connection**
```bash
# Using psql
psql -h localhost -U myuser -d mydatabase

# Test commands
\dt
SELECT version();
CREATE TABLE test (id SERIAL PRIMARY KEY, message TEXT);
INSERT INTO test (message) VALUES ('Hello PostgreSQL');
SELECT * FROM test;
```

### **Test API Connection**
```bash
# Start the API server
npm start

# Check health endpoint
curl http://localhost:3000/health

# Should show database connection status
```

---

## 🛠️ **TROUBLESHOOTING**

### **MongoDB Issues:**
```bash
# Check if MongoDB is running
tasklist | findstr mongod

# Check MongoDB logs
# Windows: C:\Program Files\MongoDB\Server\7.0\log\mongod.log

# Restart MongoDB service
net stop MongoDB
net start MongoDB
```

### **PostgreSQL Issues:**
```bash
# Check if PostgreSQL is running
tasklist | findstr postgres

# Check PostgreSQL logs
# Windows: C:\Program Files\PostgreSQL\15\data\log\

# Restart PostgreSQL service
net stop postgresql-x64-15
net start postgresql-x64-15
```

### **Connection Issues:**
1. **Check firewall settings**
2. **Verify ports are not blocked (27017, 5432)**
3. **Check credentials in .env file**
4. **Ensure databases are running**

---

## 📊 **DATABASE INITIALIZATION**

### **Run Setup Scripts**
```bash
# Setup databases with initial schema
npm run setup:db

# Or run individually
npm run setup:mongodb
npm run setup:postgresql
```

### **Add Sample Data**
```bash
# Add sample restaurants and users
node scripts/seed-data.js
```

---

## 🎯 **NEXT STEPS**

1. ✅ **Setup Databases** (MongoDB + PostgreSQL)
2. ✅ **Update .env Configuration**
3. ✅ **Test Connections**
4. ✅ **Start API Server**
5. ✅ **Test API Endpoints**

### **Ready to Launch!** 🚀

Once databases are running, your Food Delivery Platform API will be fully operational and ready to handle real traffic!
