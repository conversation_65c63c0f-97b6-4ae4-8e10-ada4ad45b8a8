# 📡 API Endpoints Documentation

## 🔗 Base URL
```
http://localhost:3000/api/v1
```

## 🔐 Authentication
All protected endpoints require JWT token in header:
```
Authorization: Bearer <your-jwt-token>
```

## 📱 **Multi-App Architecture**

### **1. Authentication Endpoints** (`/auth`)
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `POST` | `/register` | Register new user | No |
| `POST` | `/login` | User login | No |
| `POST` | `/verify-otp` | Verify OTP (Fixed: 123456) | No |
| `POST` | `/refresh-token` | Refresh access token | No |
| `POST` | `/resend-otp` | Resend OTP | No |
| `POST` | `/logout` | User logout | Yes |
| `GET` | `/me` | Get current user | Yes |

### **2. Dashboard App** (`/dashboard`) - Admin Only
| Method | Endpoint | Description | Roles |
|--------|----------|-------------|-------|
| `GET` | `/stats` | Platform statistics | Super Admin, Franchise Admin |
| `GET` | `/franchises` | Franchise management | Super Admin, Franchise Admin |
| `GET` | `/users` | User management | Super Admin, Franchise Admin |
| `GET` | `/orders` | Order management | Super Admin, Franchise Admin |

### **3. User App** (`/user`) - Customer
| Method | Endpoint | Description | Roles |
|--------|----------|-------------|-------|
| `GET` | `/profile` | Get user profile | Customer |
| `PUT` | `/profile` | Update user profile | Customer |
| `GET` | `/restaurants` | Get available restaurants | Customer |
| `GET` | `/orders` | Get user orders | Customer |
| `POST` | `/orders` | Place new order | Customer |
| `GET` | `/addresses` | Get user addresses | Customer |
| `POST` | `/addresses` | Add new address | Customer |

### **4. Restaurant Partner App** (`/restaurant-partner`) - Restaurant Owner
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/profile` | Get restaurant partner profile |
| `PUT` | `/profile` | Update restaurant partner profile |
| `GET` | `/restaurants` | Get owned restaurants |
| `POST` | `/restaurants` | Add new restaurant |
| `PUT` | `/restaurants/:id` | Update restaurant details |
| `GET` | `/restaurants/:id/menu` | Get restaurant menu |
| `POST` | `/restaurants/:id/menu` | Add menu item |
| `PUT` | `/restaurants/:id/menu/:itemId` | Update menu item |
| `DELETE` | `/restaurants/:id/menu/:itemId` | Delete menu item |
| `GET` | `/orders` | Get restaurant orders |
| `PUT` | `/orders/:id/status` | Update order status |
| `POST` | `/orders/:id/accept` | Accept order |
| `POST` | `/orders/:id/reject` | Reject order |
| `GET` | `/analytics/sales` | Sales analytics |
| `GET` | `/analytics/performance` | Performance analytics |
| `GET` | `/earnings` | Restaurant earnings |
| `GET` | `/settlements` | Settlement history |
| `PUT` | `/restaurants/:id/status` | Update restaurant status |
| `PUT` | `/restaurants/:id/hours` | Update restaurant hours |

### **5. Delivery Partner App** (`/delivery-partner`) - Delivery Partner
| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/profile` | Get delivery partner profile |
| `PUT` | `/profile` | Update delivery partner profile |
| `PUT` | `/status` | Update online/offline status |
| `PUT` | `/location` | Update current location |
| `GET` | `/orders/available` | Get available orders |
| `GET` | `/orders` | Get assigned orders |
| `POST` | `/orders/:id/accept` | Accept delivery order |
| `POST` | `/orders/:id/reject` | Reject delivery order |
| `PUT` | `/orders/:id/status` | Update delivery status |
| `POST` | `/orders/:id/pickup` | Confirm order pickup |
| `POST` | `/orders/:id/deliver` | Confirm order delivery |
| `GET` | `/orders/:id/route` | Get delivery route |
| `GET` | `/earnings` | Delivery partner earnings |
| `GET` | `/earnings/breakdown` | Detailed earnings breakdown |
| `GET` | `/analytics/performance` | Performance analytics |
| `GET` | `/availability` | Get availability schedule |
| `PUT` | `/availability` | Update availability schedule |
| `GET` | `/settlements` | Settlement history |
| `POST` | `/settlements/instant-payout` | Request instant payout |
| `POST` | `/emergency` | Send emergency alert |
| `GET` | `/support/chat` | Support chat |

## 📊 **Request/Response Examples**

### **User Registration**
```bash
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "phone": "+919876543210",
  "password": "SecurePass123",
  "role": "restaurant_owner",
  "franchiseId": "60d5ecb74b24a1234567890a",
  "profile": {
    "name": "Restaurant Owner",
    "restaurantInfo": {
      "businessType": "restaurant",
      "experience": 5
    }
  }
}
```

### **OTP Verification**
```bash
POST /api/v1/auth/verify-otp
Content-Type: application/json

{
  "phone": "+919876543210",
  "otp": "123456"
}
```

### **Restaurant Order Management**
```bash
POST /api/v1/restaurant-partner/orders/60d5ecb74b24a1234567890b/accept
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "estimatedTime": 25
}
```

### **Delivery Partner Location Update**
```bash
PUT /api/v1/delivery-partner/location
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "coordinates": [72.8777, 19.0760]
}
```

### **Earnings Query**
```bash
GET /api/v1/restaurant-partner/earnings?period=weekly
Authorization: Bearer <jwt-token>
```

## 🔒 **Role-Based Access Control**

### **User Roles**
- **Super Admin**: Full system access
- **Franchise Admin**: Franchise-level management
- **Restaurant Owner**: Restaurant management only
- **Delivery Partner**: Delivery operations only
- **Customer**: Order placement and management

### **Endpoint Access Matrix**
| Endpoint Group | Super Admin | Franchise Admin | Restaurant Owner | Delivery Partner | Customer |
|----------------|-------------|-----------------|------------------|------------------|----------|
| `/auth/*` | ✅ | ✅ | ✅ | ✅ | ✅ |
| `/dashboard/*` | ✅ | ✅ | ❌ | ❌ | ❌ |
| `/user/*` | ❌ | ❌ | ❌ | ❌ | ✅ |
| `/restaurant-partner/*` | ❌ | ❌ | ✅ | ❌ | ❌ |
| `/delivery-partner/*` | ❌ | ❌ | ❌ | ✅ | ❌ |
| `/partner/*` | ❌ | ❌ | ✅ | ✅ | ❌ |

## 📈 **Response Format**

### **Success Response**
```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data
  }
}
```

### **Error Response**
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": [] // Optional validation details
  }
}
```

## 🚀 **Status Codes**
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `429` - Too Many Requests
- `500` - Internal Server Error

## 🔄 **Real-time Features**
- Order status updates
- Delivery partner location tracking
- Restaurant availability changes
- Earnings updates
- Performance metrics

This API architecture supports:
- ✅ **Separate partner apps** for restaurants and delivery
- ✅ **Role-based access control**
- ✅ **Real-time order management**
- ✅ **Comprehensive earnings tracking**
- ✅ **Performance analytics**
- ✅ **Franchise-level configuration**
