const User = require('../models/User');
const Franchise = require('../models/Franchise');
const Restaurant = require('../models/Restaurant');
const Order = require('../models/Order');
const logger = require('../utils/logger');
const { ERROR_CODES, USER_ROLES } = require('../utils/constants');

class DashboardController {
  async getStats(req, res) {
    try {
      const user = req.user;
      let matchCondition = {};

      // If franchise admin, filter by their franchise
      if (user.role === USER_ROLES.FRANCHISE_ADMIN) {
        matchCondition.franchiseId = user.franchiseId;
      }

      // Get basic stats
      const [
        totalUsers,
        totalRestaurants,
        totalOrders,
        activeRestaurants,
        todayOrders,
        monthlyRevenue
      ] = await Promise.all([
        User.countDocuments(user.role === USER_ROLES.FRANCHISE_ADMIN ? { franchiseId: user.franchiseId } : {}),
        Restaurant.countDocuments(matchCondition),
        Order.countDocuments(matchCondition),
        Restaurant.countDocuments({ ...matchCondition, status: 'active', isCurrentlyOpen: true }),
        Order.countDocuments({
          ...matchCondition,
          createdAt: {
            $gte: new Date(new Date().setHours(0, 0, 0, 0)),
            $lt: new Date(new Date().setHours(23, 59, 59, 999))
          }
        }),
        Order.aggregate([
          {
            $match: {
              ...matchCondition,
              createdAt: {
                $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
                $lt: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
              },
              currentStatus: { $in: ['delivered', 'completed'] }
            }
          },
          {
            $group: {
              _id: null,
              totalRevenue: { $sum: '$pricing.total' }
            }
          }
        ])
      ]);

      // Get order status distribution
      const orderStatusStats = await Order.aggregate([
        { $match: matchCondition },
        {
          $group: {
            _id: '$currentStatus',
            count: { $sum: 1 }
          }
        }
      ]);

      // Get top performing restaurants
      const topRestaurants = await Restaurant.aggregate([
        { $match: { ...matchCondition, status: 'active' } },
        {
          $lookup: {
            from: 'orders',
            localField: '_id',
            foreignField: 'restaurantId',
            as: 'orders'
          }
        },
        {
          $addFields: {
            totalOrders: { $size: '$orders' },
            totalRevenue: {
              $sum: {
                $map: {
                  input: '$orders',
                  as: 'order',
                  in: '$$order.pricing.total'
                }
              }
            }
          }
        },
        { $sort: { totalRevenue: -1 } },
        { $limit: 5 },
        {
          $project: {
            name: 1,
            totalOrders: 1,
            totalRevenue: 1,
            ratings: 1,
            cuisine: '$businessInfo.cuisine'
          }
        }
      ]);

      res.json({
        success: true,
        message: 'Dashboard statistics retrieved successfully',
        data: {
          overview: {
            totalUsers,
            totalRestaurants,
            totalOrders,
            activeRestaurants,
            todayOrders,
            monthlyRevenue: monthlyRevenue[0]?.totalRevenue || 0
          },
          orderStats: {
            statusDistribution: orderStatusStats.reduce((acc, stat) => {
              acc[stat._id] = stat.count;
              return acc;
            }, {}),
            todayOrders
          },
          topRestaurants,
          period: {
            from: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString(),
            to: new Date().toISOString()
          }
        }
      });

    } catch (error) {
      logger.error('Dashboard stats error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to retrieve dashboard statistics'
        }
      });
    }
  }

  async getFranchises(req, res) {
    try {
      const user = req.user;
      const { page = 1, limit = 10, search, status } = req.query;

      let query = {};

      // If franchise admin, only show their franchise
      if (user.role === USER_ROLES.FRANCHISE_ADMIN) {
        query._id = user.franchiseId;
      }

      // Add search filter
      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { 'owner.name': { $regex: search, $options: 'i' } },
          { 'location.city': { $regex: search, $options: 'i' } }
        ];
      }

      // Add status filter
      if (status) {
        query.status = status;
      }

      const skip = (parseInt(page) - 1) * parseInt(limit);

      const [franchises, total] = await Promise.all([
        Franchise.find(query)
          .populate('totalRestaurants')
          .skip(skip)
          .limit(parseInt(limit))
          .sort({ createdAt: -1 }),
        Franchise.countDocuments(query)
      ]);

      // Get additional stats for each franchise
      const franchisesWithStats = await Promise.all(
        franchises.map(async (franchise) => {
          const [restaurantCount, orderCount, monthlyRevenue] = await Promise.all([
            Restaurant.countDocuments({ franchiseId: franchise._id }),
            Order.countDocuments({ franchiseId: franchise._id }),
            Order.aggregate([
              {
                $match: {
                  franchiseId: franchise._id,
                  createdAt: {
                    $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
                  },
                  currentStatus: { $in: ['delivered', 'completed'] }
                }
              },
              {
                $group: {
                  _id: null,
                  revenue: { $sum: '$pricing.total' }
                }
              }
            ])
          ]);

          return {
            ...franchise.toObject(),
            stats: {
              totalRestaurants: restaurantCount,
              totalOrders: orderCount,
              monthlyRevenue: monthlyRevenue[0]?.revenue || 0
            }
          };
        })
      );

      res.json({
        success: true,
        message: 'Franchises retrieved successfully',
        data: franchisesWithStats,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });

    } catch (error) {
      logger.error('Get franchises error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to retrieve franchises'
        }
      });
    }
  }

  async getUsers(req, res) {
    try {
      const user = req.user;
      const { page = 1, limit = 10, search, role, status } = req.query;

      let query = {};

      // If franchise admin, filter by their franchise
      if (user.role === USER_ROLES.FRANCHISE_ADMIN) {
        query.franchiseId = user.franchiseId;
      }

      // Add search filter
      if (search) {
        query.$or = [
          { email: { $regex: search, $options: 'i' } },
          { 'profile.name': { $regex: search, $options: 'i' } },
          { phone: { $regex: search, $options: 'i' } }
        ];
      }

      // Add role filter
      if (role) {
        query.role = role;
      }

      // Add status filter
      if (status) {
        query.status = status;
      }

      const skip = (parseInt(page) - 1) * parseInt(limit);

      const [users, total] = await Promise.all([
        User.find(query)
          .populate('franchiseId', 'name location.city')
          .skip(skip)
          .limit(parseInt(limit))
          .sort({ createdAt: -1 }),
        User.countDocuments(query)
      ]);

      res.json({
        success: true,
        message: 'Users retrieved successfully',
        data: users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });

    } catch (error) {
      logger.error('Get users error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to retrieve users'
        }
      });
    }
  }

  async getOrders(req, res) {
    try {
      const user = req.user;
      const { page = 1, limit = 10, status, dateFrom, dateTo, restaurantId } = req.query;

      let query = {};

      // If franchise admin, filter by their franchise
      if (user.role === USER_ROLES.FRANCHISE_ADMIN) {
        query.franchiseId = user.franchiseId;
      }

      // Add status filter
      if (status) {
        query.currentStatus = status;
      }

      // Add date range filter
      if (dateFrom || dateTo) {
        query.createdAt = {};
        if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
        if (dateTo) query.createdAt.$lte = new Date(dateTo);
      }

      // Add restaurant filter
      if (restaurantId) {
        query.restaurantId = restaurantId;
      }

      const skip = (parseInt(page) - 1) * parseInt(limit);

      const [orders, total] = await Promise.all([
        Order.find(query)
          .populate('customerId', 'profile.name email phone')
          .populate('restaurantId', 'name businessInfo.cuisine location.address')
          .populate('deliveryPartnerId', 'profile.name phone')
          .skip(skip)
          .limit(parseInt(limit))
          .sort({ createdAt: -1 }),
        Order.countDocuments(query)
      ]);

      res.json({
        success: true,
        message: 'Orders retrieved successfully',
        data: orders,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      });

    } catch (error) {
      logger.error('Get orders error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: ERROR_CODES.INTERNAL_ERROR,
          message: 'Failed to retrieve orders'
        }
      });
    }
  }
}

module.exports = new DashboardController();
