const { ORDER_STATUS, USER_ROLES } = require('../utils/constants');

class ValidationService {
  validateStatusUpdate(order, newStatus, user) {
    const currentStatus = order.currentStatus;
    const userRole = user.role;

    // Define valid status transitions
    const validTransitions = {
      [ORDER_STATUS.PLACED]: [ORDER_STATUS.ACCEPTED, ORDER_STATUS.REJECTED],
      [ORDER_STATUS.ACCEPTED]: [ORDER_STATUS.PREPARING],
      [ORDER_STATUS.PREPARING]: [ORDER_STATUS.READY],
      [ORDER_STATUS.READY]: [ORDER_STATUS.PICKED_UP],
      [ORDER_STATUS.PICKED_UP]: [ORDER_STATUS.OUT_FOR_DELIVERY],
      [ORDER_STATUS.OUT_FOR_DELIVERY]: [ORDER_STATUS.DELIVERED],
    };

    // Check if transition is valid
    if (!validTransitions[currentStatus] || !validTransitions[currentStatus].includes(newStatus)) {
      return { 
        allowed: false, 
        message: `Cannot transition from ${currentStatus} to ${newStatus}` 
      };
    }

    // Check user permissions for status updates
    const permissionCheck = this.checkStatusUpdatePermissions(newStatus, userRole, order, user);
    if (!permissionCheck.allowed) {
      return permissionCheck;
    }

    return { allowed: true };
  }

  checkStatusUpdatePermissions(newStatus, userRole, order, user) {
    const restaurantStatuses = [
      ORDER_STATUS.ACCEPTED, 
      ORDER_STATUS.REJECTED, 
      ORDER_STATUS.PREPARING, 
      ORDER_STATUS.READY
    ];
    
    const deliveryStatuses = [
      ORDER_STATUS.PICKED_UP, 
      ORDER_STATUS.OUT_FOR_DELIVERY, 
      ORDER_STATUS.DELIVERED
    ];

    // Restaurant owner permissions
    if (restaurantStatuses.includes(newStatus)) {
      if (userRole !== USER_ROLES.RESTAURANT_OWNER) {
        return { 
          allowed: false, 
          message: 'Only restaurant owners can update this status' 
        };
      }
      
      // Additional check: restaurant owner can only update their own restaurant's orders
      // This would require populating restaurant data or checking ownership
    }

    // Delivery partner permissions
    if (deliveryStatuses.includes(newStatus)) {
      if (userRole !== USER_ROLES.DELIVERY_PARTNER) {
        return { 
          allowed: false, 
          message: 'Only delivery partners can update this status' 
        };
      }
    }

    // Admin permissions (can update any status)
    if (userRole === USER_ROLES.SUPER_ADMIN || userRole === USER_ROLES.FRANCHISE_ADMIN) {
      return { allowed: true };
    }

    return { allowed: true };
  }

  validateOrderItems(items) {
    if (!items || !Array.isArray(items) || items.length === 0) {
      return {
        valid: false,
        message: 'Order must contain at least one item'
      };
    }

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      
      if (!item.menuItemId) {
        return {
          valid: false,
          message: `Item ${i + 1}: Menu item ID is required`
        };
      }

      if (!item.quantity || item.quantity < 1) {
        return {
          valid: false,
          message: `Item ${i + 1}: Quantity must be at least 1`
        };
      }

      if (item.quantity > 10) {
        return {
          valid: false,
          message: `Item ${i + 1}: Maximum quantity is 10 per item`
        };
      }

      // Validate addons if present
      if (item.addons && Array.isArray(item.addons)) {
        for (let j = 0; j < item.addons.length; j++) {
          const addon = item.addons[j];
          if (!addon.name || !addon.quantity || addon.quantity < 1) {
            return {
              valid: false,
              message: `Item ${i + 1}, Addon ${j + 1}: Invalid addon data`
            };
          }
        }
      }
    }

    return { valid: true };
  }

  validateDeliveryAddress(address) {
    const requiredFields = ['address', 'coordinates'];
    
    for (const field of requiredFields) {
      if (!address[field]) {
        return {
          valid: false,
          message: `Delivery address ${field} is required`
        };
      }
    }

    // Validate coordinates
    if (!Array.isArray(address.coordinates) || address.coordinates.length !== 2) {
      return {
        valid: false,
        message: 'Delivery address coordinates must be [longitude, latitude]'
      };
    }

    const [longitude, latitude] = address.coordinates;
    if (typeof longitude !== 'number' || typeof latitude !== 'number') {
      return {
        valid: false,
        message: 'Delivery address coordinates must be valid numbers'
      };
    }

    // Basic coordinate range validation
    if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90) {
      return {
        valid: false,
        message: 'Delivery address coordinates are out of valid range'
      };
    }

    return { valid: true };
  }

  validatePaymentMethod(paymentMethod) {
    const validMethods = ['cod', 'online'];
    
    if (!paymentMethod || !validMethods.includes(paymentMethod)) {
      return {
        valid: false,
        message: 'Payment method must be either "cod" or "online"'
      };
    }

    return { valid: true };
  }

  validateRestaurantOperationalHours(restaurant) {
    const now = new Date();
    const currentDay = now.toLocaleDateString('en-US', { weekday: 'lowercase' });
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

    if (!restaurant.operationalInfo || !restaurant.operationalInfo.businessHours) {
      return {
        valid: false,
        message: 'Restaurant operational hours not configured'
      };
    }

    const todayHours = restaurant.operationalInfo.businessHours[currentDay];
    if (!todayHours || !todayHours.isOpen) {
      return {
        valid: false,
        message: 'Restaurant is closed today'
      };
    }

    if (currentTime < todayHours.open || currentTime > todayHours.close) {
      return {
        valid: false,
        message: `Restaurant is closed. Open hours: ${todayHours.open} - ${todayHours.close}`
      };
    }

    return { valid: true };
  }

  validateOrderCancellation(order, userId, userRole) {
    // Check ownership
    if (order.customerId.toString() !== userId.toString() && 
        userRole !== USER_ROLES.SUPER_ADMIN && 
        userRole !== USER_ROLES.FRANCHISE_ADMIN) {
      return {
        valid: false,
        message: 'You can only cancel your own orders'
      };
    }

    // Check if order can be cancelled based on status
    const cancellableStatuses = [
      ORDER_STATUS.PLACED, 
      ORDER_STATUS.ACCEPTED, 
      ORDER_STATUS.PREPARING
    ];

    if (!cancellableStatuses.includes(order.currentStatus)) {
      return {
        valid: false,
        message: 'Order cannot be cancelled at this stage'
      };
    }

    // Check time-based cancellation policy
    const orderTime = new Date(order.createdAt);
    const now = new Date();
    const timeDifference = (now - orderTime) / (1000 * 60); // minutes

    // Allow cancellation within 5 minutes for placed orders
    if (order.currentStatus === ORDER_STATUS.PLACED && timeDifference > 5) {
      return {
        valid: false,
        message: 'Order can only be cancelled within 5 minutes of placement'
      };
    }

    return { valid: true };
  }

  validateUserAccess(resource, userId, userRole, resourceOwnerId = null) {
    // Super admin and franchise admin have access to everything
    if (userRole === USER_ROLES.SUPER_ADMIN || userRole === USER_ROLES.FRANCHISE_ADMIN) {
      return { valid: true };
    }

    // Resource owner has access
    if (resourceOwnerId && resourceOwnerId.toString() === userId.toString()) {
      return { valid: true };
    }

    // Customer can only access their own resources
    if (userRole === USER_ROLES.CUSTOMER) {
      if (resource.customerId && resource.customerId.toString() === userId.toString()) {
        return { valid: true };
      }
    }

    // Restaurant owner can access their restaurant's orders
    if (userRole === USER_ROLES.RESTAURANT_OWNER) {
      if (resource.restaurantId && resource.restaurantId.ownerId?.toString() === userId.toString()) {
        return { valid: true };
      }
    }

    // Delivery partner can access assigned orders
    if (userRole === USER_ROLES.DELIVERY_PARTNER) {
      if (resource.deliveryPartnerId && resource.deliveryPartnerId.toString() === userId.toString()) {
        return { valid: true };
      }
    }

    return {
      valid: false,
      message: 'Access denied'
    };
  }

  validateBusinessHours(businessHours) {
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    
    for (const day of days) {
      if (!businessHours[day]) {
        return {
          valid: false,
          message: `Business hours for ${day} are required`
        };
      }

      const dayHours = businessHours[day];
      if (dayHours.isOpen) {
        if (!dayHours.open || !dayHours.close) {
          return {
            valid: false,
            message: `Open and close times are required for ${day}`
          };
        }

        // Validate time format (HH:MM)
        const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
        if (!timeRegex.test(dayHours.open) || !timeRegex.test(dayHours.close)) {
          return {
            valid: false,
            message: `Invalid time format for ${day}. Use HH:MM format`
          };
        }
      }
    }

    return { valid: true };
  }
}

module.exports = new ValidationService();
