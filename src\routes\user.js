const express = require('express');
const authMiddleware = require('../middleware/auth');
const userController = require('../controllers/userController');
const orderController = require('../controllers/orderController');
const { USER_ROLES } = require('../utils/constants');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: User
 *   description: Customer user operations (profile, restaurants, orders, addresses)
 */

// Apply authentication middleware
router.use(authMiddleware.authenticate);

// User profile routes
router.get('/profile', authMiddleware.authorize([USER_ROLES.CUSTOMER]), userController.getProfile);
router.put('/profile', authMiddleware.authorize([USER_ROLES.CUSTOMER]), userController.updateProfile);

/**
 * @swagger
 * /user/restaurants:
 *   get:
 *     summary: Discover restaurants with geospatial search
 *     tags: [User]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of restaurants per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by restaurant name or cuisine
 *         example: "burger"
 *       - in: query
 *         name: cuisine
 *         schema:
 *           type: string
 *         description: Filter by cuisine types (comma-separated)
 *         example: "american,fast_food"
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [rating, deliveryTime, distance, popularity]
 *           default: rating
 *         description: Sort restaurants by criteria
 *       - in: query
 *         name: latitude
 *         schema:
 *           type: number
 *         description: User's latitude for location-based search
 *         example: 19.0760
 *       - in: query
 *         name: longitude
 *         schema:
 *           type: number
 *         description: User's longitude for location-based search
 *         example: 72.8777
 *       - in: query
 *         name: maxDistance
 *         schema:
 *           type: integer
 *           default: 10000
 *         description: Maximum distance in meters
 *     responses:
 *       200:
 *         description: Restaurants retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/Restaurant'
 *                           - type: object
 *                             properties:
 *                               distance:
 *                                 type: number
 *                                 example: 2.5
 *                                 description: Distance in kilometers
 *                               isCurrentlyOpen:
 *                                 type: boolean
 *                                 example: true
 *                               estimatedDeliveryTime:
 *                                 type: number
 *                                 example: 35
 *                                 description: Estimated delivery time in minutes
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         pages:
 *                           type: integer
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
// Restaurant discovery routes
router.get('/restaurants', authMiddleware.authorize([USER_ROLES.CUSTOMER]), userController.getRestaurants);

// Order management routes
router.get('/orders', authMiddleware.authorize([USER_ROLES.CUSTOMER]), userController.getOrders);
router.post('/orders', authMiddleware.authorize([USER_ROLES.CUSTOMER]), orderController.placeOrder);

// Address management routes
router.get('/addresses', authMiddleware.authorize([USER_ROLES.CUSTOMER]), userController.getAddresses);
router.post('/addresses', authMiddleware.authorize([USER_ROLES.CUSTOMER]), userController.addAddress);

module.exports = router;
